import re

def extract_text_from_xml():
    """Extract clean text from the XML content"""
    try:
        with open("extracted_content.txt", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Find all text within <w:t> tags
        text_pattern = r'<w:t[^>]*>(.*?)</w:t>'
        matches = re.findall(text_pattern, content, re.DOTALL)
        
        # Clean and join the text
        clean_text = []
        for match in matches:
            # Remove any remaining XML entities and clean up
            text = match.strip()
            if text and text not in ['', ' ']:
                # Handle XML entities
                text = text.replace('&amp;', '&')
                text = text.replace('&lt;', '<')
                text = text.replace('&gt;', '>')
                text = text.replace('&quot;', '"')
                text = text.replace('&apos;', "'")
                clean_text.append(text)
        
        # Join all text and format
        full_text = ' '.join(clean_text)
        
        # Split into paragraphs and clean up
        paragraphs = []
        current_paragraph = []
        
        for text in clean_text:
            if text.strip():
                current_paragraph.append(text)
                # Check if this might be end of paragraph (simple heuristic)
                if text.endswith('.') or text.endswith(':') or len(text) > 50:
                    if current_paragraph:
                        paragraphs.append(' '.join(current_paragraph))
                        current_paragraph = []
        
        # Add any remaining text
        if current_paragraph:
            paragraphs.append(' '.join(current_paragraph))
        
        # Format the final document
        formatted_content = []
        for para in paragraphs:
            para = para.strip()
            if para:
                # Detect headings (simple heuristic)
                if (len(para) < 100 and 
                    (para.startswith('📋') or para.startswith('🎯') or 
                     para.startswith('👥') or para.endswith(':') or
                     'Heading' in para or para.isupper())):
                    formatted_content.append(f"\n## {para}\n")
                else:
                    formatted_content.append(para)
        
        return '\n\n'.join(formatted_content)
        
    except Exception as e:
        return f"Error processing content: {e}"

def main():
    """Main function to extract and save clean content"""
    try:
        clean_content = extract_text_from_xml()
        
        # Save the clean content
        with open("clean_content.txt", "w", encoding="utf-8") as f:
            f.write(clean_content)
        
        print("Clean content extracted and saved to clean_content.txt")
        print(f"Content length: {len(clean_content)} characters")
        
        # Show preview
        lines = clean_content.split('\n')
        preview_lines = lines[:20]
        print("\nPreview:")
        for line in preview_lines:
            print(line)
        
        if len(lines) > 20:
            print(f"\n... and {len(lines) - 20} more lines")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
