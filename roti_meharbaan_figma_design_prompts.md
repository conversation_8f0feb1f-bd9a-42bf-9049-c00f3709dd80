# Roti Meharbaan Mobile Application - Figma Design Prompts

## Table of Contents

1. [Introduction](#introduction)
2. [Design Principles & Guidelines](#design-principles--guidelines)
3. [Design Prompt 1: User Registration & Onboarding Screens](#design-prompt-1-user-registration--onboarding-screens)
4. [Design Prompt 2: Laborer (Mazdoor) Dashboard Screen](#design-prompt-2-laborer-mazdoor-dashboard-screen)
5. [Design Prompt 3: QR Code Scanner Screen](#design-prompt-3-qr-code-scanner-screen)
6. [Design Prompt 4: Tandoor Owner Dashboard Screen](#design-prompt-4-tandoor-owner-dashboard-screen)
7. [Design Prompt 5: Donor Dashboard Screen](#design-prompt-5-donor-dashboard-screen)
8. [Design Prompt 6: Payment & Transaction Screens](#design-prompt-6-payment--transaction-screens)
9. [Design Prompt 7: Map & Location Screens](#design-prompt-7-map--location-screens)
10. [Design Prompt 8: Profile & Settings Screens](#design-prompt-8-profile--settings-screens)
11. [Design Prompt 9: Analytics & Reporting Screens](#design-prompt-9-analytics--reporting-screens)
12. [Design Prompt 10: Notification & Communication Screens](#design-prompt-10-notification--communication-screens)

---

## Introduction

This document provides comprehensive Figma design prompts for the **Roti Meharbaan** mobile application, a social impact platform that connects laborers, tandoor owners, and donors to provide subsidized bread (roti) to workers in Pakistan.

### Application Overview
- **Product Name:** Roti Meharbaan (روٹی مہربان)
- **Platform:** Android (Primary), iOS (Future)
- **Target Users:** Laborers/Mazdoors, Tandoor Owners, Donors
- **Core Mission:** Provide affordable nutrition (3 roti for 5 PKR instead of 60 PKR) through a donor-funded ecosystem

### Purpose of This Document
These design prompts serve as detailed specifications for creating pixel-perfect Figma mockups that:
- Address the needs of three distinct user types with varying tech literacy levels
- Support multi-language functionality (English/Urdu with RTL text support)
- Ensure accessibility for users with limited formal education
- Maintain cultural sensitivity and local context for Pakistani users
- Create a cohesive, trustworthy, and impactful user experience

---

## Design Principles & Guidelines

### Core Design Principles
1. **Accessibility First:** Large touch targets, high contrast, simple navigation
2. **Cultural Sensitivity:** Islamic color preferences, local context, respectful imagery
3. **Multi-Language Support:** Seamless English/Urdu switching with RTL text support
4. **Trust & Security:** Clear security indicators, transparent processes
5. **Social Impact Focus:** Emphasize community benefit and positive outcomes

### Global Design Standards
- **Minimum Touch Target:** 48dp for all interactive elements
- **Color Contrast:** 4.5:1 minimum ratio for text readability
- **Typography:** 18sp body text, 24sp headings minimum
- **Spacing Grid:** 8dp, 16dp, 24dp consistent spacing system
- **Screen Sizes:** Design for 5" to 6.5" Android devices

### Color Palette
- **Primary:** Deep Green (#2E7D32) - Growth, prosperity, Islamic context
- **Secondary:** Warm Orange (#FF8F00) - Call-to-action, energy
- **Background:** Light Cream (#FAFAFA) - Warmth, readability
- **Success:** Bright Green (#4CAF50) - Positive actions
- **Warning:** Amber (#FFC107) - Caution states
- **Error:** Red (#F44336) - Error states

### Typography
- **English:** Noto Sans (Regular, Medium, Bold)
- **Urdu:** Noto Nastaliq Urdu (Regular, Bold)
- **Hierarchy:** Clear distinction between headings, body text, and captions

---

## Design Prompt 1: User Registration & Onboarding Screens

### Screen Category
**User Registration & Authentication Flow**

### Target User Type
All users (Laborer/Mazdoor, Tandoor Owner, Donor)

### Primary Use Case
First-time user registration, role selection, and account setup to onboard users into the Roti Meharbaan ecosystem

### Detailed Requirements

#### Layout Specifications
- **Screen Structure:** Vertical scrolling layout with 24px side margins
- **Header Area:** 80px height with app logo and language toggle button
- **Main Content:** Centered form elements with consistent spacing
- **Footer Area:** 100px height with primary navigation buttons
- **Safe Areas:** Account for various Android screen sizes (5" to 6.5")

#### Multi-Language Support
- **Language Toggle:** Prominent English/Urdu switch in top-right corner
- **Text Direction:** RTL alignment for Urdu content with proper text flow
- **Font Selection:** Noto Sans for English, Noto Nastaliq Urdu for Urdu
- **Dynamic Sizing:** Accommodate longer Urdu translations without breaking layout
- **Cultural Colors:** Green tones for Islamic context, warm welcoming colors

#### Accessibility Considerations
- **Touch Targets:** Minimum 48dp for all buttons and interactive elements
- **Contrast Ratios:** 4.5:1 minimum for all text against backgrounds
- **Font Sizes:** 18sp minimum for body text, 24sp for headings
- **Visual Hierarchy:** Clear spacing using 8dp, 16dp, 24dp grid system
- **Screen Reader:** Descriptive labels for voice-over support

#### Visual Design Elements
- **Primary Color:** Deep green (#2E7D32) for trust and growth
- **Accent Color:** Warm orange (#FF8F00) for call-to-action elements
- **Background:** Light cream (#FAFAFA) for warmth and comfort
- **Typography:** Bold headings, medium weight body text, clear button labels
- **Icons:** Simple, universally understood symbols (phone, person, location, heart)

### Key Features to Include

#### 1. Welcome Screen
- **App Logo:** Prominent display with Urdu and English taglines
- **Purpose Explanation:** Brief, illustrated description of app benefits
- **Get Started Button:** Large, prominent call-to-action
- **Language Selection:** Flag icons with immediate language switching
- **Visual Elements:** Warm illustrations showing community impact

#### 2. Role Selection Screen
- **Three User Type Cards:** Large, distinct cards for each role
  - Laborer: Construction worker icon with hard hat
  - Tandoor Owner: Tandoor/bread icon with business elements
  - Donor: Heart/helping hands icon with giving symbolism
- **Role Descriptions:** Simple, clear explanations in both languages
- **Visual Hierarchy:** Equal prominence for all three options
- **Continue Button:** Activates only after role selection

#### 3. Phone Verification Screen
- **Country Code:** Pakistan (+92) as default with dropdown selector
- **Phone Input:** Large, formatted input field with number masking
- **OTP Screen:** 6-digit code input with large, clear boxes
- **Resend Timer:** Countdown display with resend functionality
- **Verification Button:** Clear loading states and success feedback

#### 4. Profile Setup Screen
- **Photo Upload:** Camera/gallery options with placeholder avatar
- **Form Fields:** Role-specific information collection
  - Laborer: Name, work area, emergency contact
  - Tandoor: Business name, address, operating hours
  - Donor: Name, preferred donation frequency
- **Location Picker:** Map integration for address selection
- **Terms Agreement:** Expandable terms with clear checkbox
- **Progress Indicator:** Visual completion status

### Design Considerations
- **Card-Based Layout:** Easy scanning and visual organization
- **Progressive Disclosure:** Avoid overwhelming users with too much information
- **Contextual Help:** Tooltips and help text for complex fields
- **One-Handed Use:** Thumb-friendly navigation and button placement
- **Error Handling:** Clear validation feedback with helpful suggestions
- **Offline Support:** Basic registration data storage for poor connectivity

---

## Design Prompt 2: Laborer (Mazdoor) Dashboard Screen

### Screen Category
**Laborer Main Dashboard**

### Target User Type
Laborer/Mazdoor (Primary beneficiary)

### Primary Use Case
Daily roti consumption tracking, savings monitoring, tandoor discovery, and quick access to core app functionality

### Detailed Requirements

#### Layout Specifications
- **Status Bar:** 120px height with personalized greeting and daily allowance
- **Quick Stats:** 200px section with consumption and savings cards
- **Map Section:** 300px interactive map showing nearby tandoors
- **Bottom Navigation:** 80px with 4 main app sections
- **Floating Action:** 56dp QR scanner button for quick access

#### Data Display Requirements
- **Daily Counter:** "2 of 3 roti remaining today" with visual progress
- **Savings Display:** "You saved PKR 1,650 this month" with trend chart
- **Tandoor List:** Distance, availability status, ratings, walking time
- **Transaction History:** Simple icons with amounts and timestamps
- **Contextual Messages:** Weather-appropriate greetings and motivational content

#### Interactive Elements
- **Pull-to-Refresh:** Real-time data updates with loading animation
- **Swipeable Cards:** Horizontal scrolling for quick actions
- **Tap-to-Expand:** Detailed information without navigation
- **Voice Commands:** Urdu/English voice support for accessibility
- **Haptic Feedback:** Confirmation for successful actions

### Key Features to Include

#### 1. Status Overview Card
- **Daily Allowance Display:** Large, colorful remaining roti count
- **Progress Indicator:** Circular or bar chart showing 2/3 consumed
- **Reset Timer:** Countdown to midnight refresh
- **Emergency Button:** Quick access to help if needed
- **Motivational Message:** Encouraging text about savings impact

#### 2. Savings Tracker
- **Monthly Amount:** Large, bold PKR savings display
- **Comparison Chart:** Visual comparison with previous months
- **Trend Visualization:** Simple line chart showing progress
- **Impact Statement:** "This money helped your family" messaging
- **Goal Setting:** Optional savings targets with progress

#### 3. Nearby Tandoors Map
- **Interactive Map:** Real-time tandoor locations with status markers
- **Distance Display:** Walking time and distance estimates
- **Availability Indicators:** Green (open), yellow (busy), red (closed)
- **Navigate Buttons:** Direct integration with map applications
- **Filter Options:** Open now, highest rated, closest distance

#### 4. Quick Actions Section
- **Scan QR Code:** Large, prominent button with camera icon
- **Find Tandoor:** Location-based search with map integration
- **View History:** Transaction history with simple timeline
- **Get Help:** Emergency contact and support options
- **Share App:** Referral system for other laborers

### Design Considerations
- **Outdoor Visibility:** High contrast for bright sunlight conditions
- **Offline Indicators:** Clear status when connectivity is poor
- **Dirty Hands:** Larger touch targets for work environment use
- **Battery Saving:** Efficient location services and data usage
- **Audio Feedback:** Sound confirmation for successful transactions
- **Simple Language:** Clear, jargon-free text in both languages

---

## Design Prompt 3: QR Code Scanner Screen

### Screen Category
**QR Code Scanner Interface**

### Target User Type
Laborer/Mazdoor (primary), Tandoor Owner (verification)

### Primary Use Case
Scanning QR codes for subsidized roti transactions with real-time validation and confirmation

### Detailed Requirements

#### Layout Specifications
- **Full-Screen Camera:** Viewfinder with overlay guides and controls
- **Top Control Bar:** 80px with close, flash toggle, and help buttons
- **Scanning Area:** 280x280px center frame with animated borders
- **Instruction Panel:** 150px bottom area with guidance text
- **Confirmation Overlay:** Full-screen modal when QR code detected

#### Visual Design Elements
- **Scanning Frame:** Bright green (#4CAF50) animated corner brackets
- **Background Overlay:** Semi-transparent black (60% opacity)
- **Success Animation:** Green checkmark with haptic feedback
- **Error State:** Red warning icon with clear error messaging
- **Loading State:** Pulsing animation during server validation

#### Interactive Elements
- **Tap-to-Focus:** Camera focus control with visual feedback
- **Pinch-to-Zoom:** Zoom functionality for distant QR codes
- **Flash Toggle:** Low-light assistance with brightness control
- **Manual Entry:** Alternative input for damaged QR codes
- **Voice Guidance:** Audio instructions for visually impaired users

### Key Features to Include

#### 1. Camera Interface
- **Real-Time Feed:** Auto-focus camera with smooth performance
- **QR Detection:** Instant visual feedback when code is recognized
- **Positioning Guide:** Overlay instructions for optimal scanning
- **Lighting Control:** Flash toggle with automatic low-light detection
- **Stability Guide:** Visual aids for steady hand positioning

#### 2. Validation Feedback
- **Detection Confirmation:** Immediate visual response when QR found
- **Processing Animation:** Loading indicator during server validation
- **Success/Error States:** Clear messaging with appropriate colors
- **Audio Feedback:** Sound effects for successful scans
- **Vibration:** Haptic confirmation for accessibility

#### 3. Transaction Preview
- **Tandoor Information:** Name, location, and verification status
- **Price Display:** Prominent 5 PKR amount with savings highlight
- **Allowance Update:** Remaining daily roti count after purchase
- **Confirm Button:** Large, secure confirmation with final check
- **Cancel Option:** Easy exit without completing transaction

#### 4. Error Handling
- **Invalid QR:** Clear messaging with retry instructions
- **Daily Limit:** Friendly notification when limit exceeded
- **Network Issues:** Offline queue with sync notification
- **Expired Code:** Helpful message with refresh suggestion
- **Security Alerts:** Fraud prevention with reporting options

### Design Considerations
- **Lighting Optimization:** Performance in various lighting conditions
- **First-Time Tutorial:** Overlay instructions for new users
- **Hand Stability:** Guides and tips for shaky hands
- **Accessibility:** Alternative input methods for disabilities
- **Security Measures:** Screenshot prevention and fraud detection

---

## Design Prompt 4: Tandoor Owner Dashboard Screen

### Screen Category
**Tandoor Business Dashboard**

### Target User Type
Tandoor Owner

### Primary Use Case
Business analytics monitoring, QR code management, transaction oversight, and customer verification

### Detailed Requirements

#### Layout Specifications
- **Business Header:** 100px with business name, QR status, notifications
- **Analytics Grid:** 2x2 cards (160px each) showing key business metrics
- **QR Management:** 200px section with current code and controls
- **Transaction List:** Expandable list with customer details
- **Bottom Navigation:** Business tools, settings, and support access

#### Data Display Requirements
- **Sales Metrics:** Daily counts of regular vs subsidized roti sales
- **Revenue Tracking:** Total earnings, pending payments, profit margins
- **Customer Analytics:** New vs returning customers, satisfaction ratings
- **QR Code Status:** Active status, expiration time, usage statistics
- **Transaction History:** Timestamps, customer verification, amounts

#### Interactive Elements
- **Swipe Navigation:** Between different time periods and metrics
- **Expandable Cards:** Tap for detailed analytics views
- **Long-Press Actions:** Quick actions on transaction items
- **Pull-to-Refresh:** Real-time business data updates
- **Export Functions:** Report generation and sharing capabilities

### Key Features to Include

#### 1. Business Metrics Cards
- **Daily Sales:** Today's count with yesterday comparison
- **Revenue Breakdown:** Subsidized vs regular sales analysis
- **Customer Ratings:** Satisfaction scores with feedback summary
- **Peak Hours:** Visual chart showing busiest times
- **Growth Trends:** Week/month comparison with percentage changes

#### 2. QR Code Management
- **Current QR Display:** Large, scannable preview of active code
- **Generate New:** Button with confirmation dialog
- **Print/Share Options:** Physical display and digital sharing
- **Expiration Timer:** Countdown with auto-refresh settings
- **Usage Statistics:** Scan count and success rate tracking

#### 3. Customer Verification
- **Recent Customers:** List with verification status indicators
- **Flagged Transactions:** Items requiring manual review
- **Feedback System:** Customer ratings and comments display
- **Fraud Alerts:** Security notifications and reporting tools
- **Verification Tools:** Customer ID checking and validation

#### 4. Financial Overview
- **Daily Earnings:** Summary with detailed breakdown
- **Payment Status:** Pending settlements and notifications
- **Settlement Schedule:** Payment dates and history
- **Tax Documentation:** Receipt generation and record keeping
- **Profit Analysis:** Cost vs revenue with margin calculations

### Design Considerations
- **Quick Glance Design:** Information accessible during busy periods
- **Printer Integration:** QR code layouts optimized for printing
- **Multi-User Access:** Role-based permissions for staff members
- **Offline Recording:** Transaction logging without internet
- **Tablet Compatibility:** Responsive design for larger screens

---

## Design Prompt 5: Donor Dashboard Screen

### Screen Category
**Donor Impact Dashboard**

### Target User Type
Donor

### Primary Use Case
Impact visualization, donation management, transparency reporting, and social engagement

### Detailed Requirements

#### Layout Specifications
- **Hero Impact Section:** 200px with total impact statistics and visuals
- **Visualization Area:** 300px with charts, maps, and infographics
- **Donation History:** 250px section with transaction records
- **Action Area:** 120px for new donations and recurring setup
- **Navigation Tabs:** Time periods and impact category filters

#### Data Display Requirements
- **Total Impact:** Meals provided with animated counters
- **Geographic Distribution:** Heat map showing donation reach
- **Beneficiary Stories:** Photos, testimonials, and success stories
- **Financial Transparency:** Fund allocation with percentage breakdowns
- **Tax Documentation:** Downloadable receipts and certificates

#### Visual Design Elements
- **Inspiring Palette:** Blues and greens conveying trust and growth
- **Infographic Style:** Data visualization with emotional connection
- **Photo Galleries:** Impact stories with high-quality imagery
- **Progress Indicators:** Goal tracking with visual completion bars
- **Achievement Displays:** Certificate-style recognition elements

### Key Features to Include

#### 1. Impact Visualization
- **Meal Counter:** Animated display of total meals provided
- **Geographic Map:** Heat map showing donation distribution
- **Success Stories:** Before/after narratives of beneficiary families
- **Community Impact:** Social outcomes and long-term benefits
- **Real-Time Updates:** Live impact as donations are used

#### 2. Donation Management
- **Quick Donate:** Preset amount buttons for easy giving
- **Recurring Setup:** Automated donation scheduling
- **Payment Methods:** Multiple secure payment options
- **Donation History:** Detailed records with impact tracking
- **Goal Setting:** Personal giving targets with progress

#### 3. Transparency Reports
- **Fund Allocation:** Pie charts showing money distribution
- **Administrative Costs:** Clear breakdown of operational expenses
- **Real-Time Tracking:** Live updates on donation usage
- **Audit Reports:** Third-party verification and certifications
- **Impact Metrics:** Quantified social and economic outcomes

#### 4. Social Features
- **Story Sharing:** Social media integration for impact stories
- **Friend Invitations:** Referral system for new donors
- **Leaderboards:** Optional recognition for top contributors
- **Group Challenges:** Community donation goals and campaigns
- **Achievement Badges:** Gamification elements for engagement

### Design Considerations
- **Emotional Connection:** Storytelling through visual design
- **Professional Reporting:** Business-grade financial layouts
- **Social Sharing:** Optimized content for social media platforms
- **Engagement Elements:** Gamification without trivializing impact
- **Export Options:** Multiple formats for data and reports

---

## Design Prompt 6: Payment & Transaction Screens

### Screen Category
**Payment Processing Interface**

### Target User Type
All users (context-dependent)

### Primary Use Case
Secure payment processing, transaction confirmation, and financial record keeping

### Detailed Requirements

#### Layout Specifications
- **Security Header:** 80px with SSL indicators and navigation
- **Payment Method Grid:** 200px with card-based selection layout
- **Amount Confirmation:** 150px with large, clear pricing display
- **Payment Form:** 300px with secure input fields and validation
- **Trust Footer:** 100px with security badges and encryption info

#### Security Considerations
- **SSL Indicators:** Prominent security certificate displays
- **Masked Inputs:** Secure fields for sensitive information
- **Biometric Auth:** Fingerprint/face recognition integration
- **Two-Factor:** Additional verification for large amounts
- **Privacy Policy:** Clear data handling and protection information

#### Payment Methods Support
- **Easypaisa:** Branded integration with native interface
- **Card Processing:** Secure forms with real-time validation
- **Bank Transfer:** Account details with verification
- **Mobile Wallets:** JazzCash and other local payment options
- **Offline Recording:** Cash transaction logging capability

### Key Features to Include

#### 1. Payment Method Selection
- **Branded Buttons:** Large, recognizable payment provider logos
- **Security Badges:** Trust indicators and verification symbols
- **Processing Time:** Estimated completion time for each method
- **Fee Transparency:** Clear cost breakdown for each option
- **Availability Status:** Real-time service availability indicators

#### 2. Amount Confirmation
- **Large Display:** Bold, prominent transaction amount
- **Fee Breakdown:** Detailed cost analysis with transparency
- **Currency Support:** PKR with international options if needed
- **Impact Statement:** "This provides 12 meals" messaging
- **Comparison:** Savings vs regular price highlighting

#### 3. Secure Payment Form
- **Auto-Formatting:** Card numbers, dates, and validation codes
- **Real-Time Validation:** Immediate feedback with helpful errors
- **Save Payment:** Optional storage with security notices
- **Progress Indicator:** Multi-step payment process guidance
- **Encryption Notice:** Visible security and data protection info

#### 4. Transaction Confirmation
- **Success Animation:** Celebratory confirmation with transaction ID
- **Digital Receipt:** Comprehensive transaction details
- **Share/Save Options:** Receipt distribution and storage
- **Next Steps:** Guidance for follow-up actions
- **Support Access:** Help options if issues arise

### Design Considerations
- **Security Visualization:** Highest security standards prominently displayed
- **Payment Preferences:** Support for local payment method preferences
- **Error Recovery:** Clear retry mechanisms and alternative options
- **Offline Support:** Transaction recording for poor connectivity
- **Regulatory Compliance:** Local financial regulation adherence

---

## Design Prompt 7: Map & Location Screens

### Screen Category
**Location Services Interface**

### Target User Type
Laborer/Mazdoor (primary), Tandoor Owner (registration)

### Primary Use Case
Tandoor discovery, navigation assistance, and location-based verification

### Detailed Requirements

#### Layout Specifications
- **Full-Screen Map:** Interactive map with overlay controls
- **Search Bar:** 60px top section with location input and filters
- **Bottom Sheet:** 300px expandable tandoor details panel
- **Floating Buttons:** Location services, settings, and quick actions
- **Navigation Overlay:** Turn-by-turn directions with voice guidance

#### Map Features
- **GPS Positioning:** Real-time location with accuracy indicators
- **Tandoor Markers:** Color-coded availability status markers
- **Distance Circles:** Walking time estimates with visual radius
- **Offline Caching:** Essential map data for poor connectivity
- **View Options:** Satellite, street view, and terrain toggles

#### Interactive Elements
- **Pinch-to-Zoom:** Smooth map navigation with gesture controls
- **Marker Taps:** Quick info display for tandoor locations
- **Sheet Swipes:** Bottom panel expansion for detailed information
- **Voice Navigation:** Local language turn-by-turn directions
- **Shake Refresh:** Location updates with motion gesture

### Key Features to Include

#### 1. Tandoor Discovery
- **Interactive Markers:** Color-coded by availability (green/yellow/red)
- **List Toggle:** Switch between map and list views
- **Filter Options:** Open now, highest rated, closest distance
- **Real-Time Updates:** Live availability and status changes
- **Search Function:** Name-based and location-based search

#### 2. Navigation Integration
- **Route Planning:** Walking directions with time estimates
- **Voice Guidance:** Urdu/English turn-by-turn instructions
- **Alternative Routes:** Multiple path options with comparison
- **Landmark Navigation:** Local reference points for easier following
- **Traffic Awareness:** Real-time conditions affecting walking routes

#### 3. Location Verification
- **GPS Accuracy:** Visual indicators for location precision
- **Geofencing:** Tandoor location validation for transactions
- **Check-In System:** Laborer arrival confirmation
- **Safety Features:** Location sharing for security purposes
- **Privacy Controls:** User consent and location sharing settings

#### 4. Offline Capabilities
- **Cached Maps:** Essential area data stored locally
- **Offline Storage:** Location data sync when connection restored
- **GPS Function:** Location services without internet requirement
- **Emergency Mode:** Critical location services always available
- **Data Management:** User control over offline map storage

### Design Considerations
- **Outdoor Visibility:** High contrast for bright sunlight conditions
- **Battery Optimization:** Efficient location services and power management
- **One-Handed Use:** Map navigation optimized for single-hand operation
- **Text Directions:** Alternative to visual maps for accessibility
- **Privacy Protection:** Clear controls for location data sharing

---

## Design Prompt 8: Profile & Settings Screens

### Screen Category
**User Profile Management**

### Target User Type
All users (role-specific customization)

### Primary Use Case
Account management, app preferences, privacy controls, and user support

### Detailed Requirements

#### Layout Specifications
- **Profile Header:** 150px with photo, name, role, and verification status
- **Settings Sections:** Grouped categories with clear visual dividers
- **Control Elements:** Toggle switches, dropdowns, and selection controls
- **Help Section:** Support contacts and resource access
- **Account Actions:** Logout, deletion, and security options at bottom

#### Customization Options
- **Language Settings:** Immediate preview with RTL text switching
- **Notification Controls:** Granular settings with time-based options
- **Privacy Management:** Clear explanations with user-friendly controls
- **Accessibility Options:** Font size, contrast, and interaction preferences
- **Theme Selection:** Light, dark, and high contrast mode options

#### Role-Specific Features
- **Laborer Settings:** Daily limits, emergency contacts, work preferences
- **Tandoor Owner:** Business hours, QR settings, staff management
- **Donor Settings:** Donation preferences, impact reports, tax documentation

### Key Features to Include

#### 1. Profile Information
- **Photo Management:** Camera/gallery upload with cropping tools
- **Personal Details:** Editable information with verification status
- **Role Information:** Specific details and certifications display
- **Security Settings:** Password management and account protection
- **Verification Status:** Document upload and approval tracking

#### 2. App Preferences
- **Language Selection:** English/Urdu with immediate RTL preview
- **Notification Settings:** Time controls, frequency, and type preferences
- **Sound Controls:** Audio, vibration, and haptic feedback options
- **Data Management:** Offline sync, usage monitoring, and storage controls
- **Display Options:** Theme, font size, and contrast adjustments

#### 3. Privacy & Security
- **Location Controls:** Granular sharing permissions and history
- **Data Rights:** Export, deletion, and portability requests
- **Authentication:** Two-factor setup and biometric options
- **Login History:** Device management and security monitoring
- **Consent Management:** Permission tracking and modification

#### 4. Help & Support
- **FAQ Section:** Searchable knowledge base with categories
- **Contact Support:** Multiple channels (chat, email, phone)
- **Tutorial Access:** Video guides and interactive walkthroughs
- **Community Forum:** User discussion and peer support
- **Feedback System:** Bug reports and feature suggestions

### Design Considerations
- **Visual Grouping:** Clear section headers and logical organization
- **Immediate Feedback:** Real-time preview of setting changes
- **Explanatory Text:** Clear descriptions for complex options
- **Reset Options:** Easy restoration to default settings
- **Screen Reader:** Full accessibility support with descriptive labels

---

## Design Prompt 9: Analytics & Reporting Screens

### Screen Category
**Data Analytics Dashboard**

### Target User Type
Tandoor Owner (primary), Donor (impact reports)

### Primary Use Case
Business intelligence, impact tracking, performance monitoring, and data-driven decision making

### Detailed Requirements

#### Layout Specifications
- **Time Selector:** 80px with preset ranges and custom date options
- **Key Metrics:** 200px overview with large numbers and trend indicators
- **Chart Area:** 400px interactive visualization space
- **Data Tables:** Expandable detailed breakdowns with sorting
- **Export Controls:** 60px with format options and sharing capabilities

#### Data Visualization
- **Line Charts:** Trend analysis over time with multiple data series
- **Bar Charts:** Comparative analysis with category breakdowns
- **Pie Charts:** Distribution analysis with percentage displays
- **Heat Maps:** Geographic or time-based pattern visualization
- **Progress Bars:** Goal tracking with completion indicators

#### Interactive Features
- **Chart Drilling:** Tap for detailed data exploration
- **Category Swiping:** Horizontal navigation between metric types
- **Zoom Controls:** Pinch-to-zoom on detailed chart views
- **Filter Options:** Data table sorting and filtering capabilities
- **Real-Time Updates:** Live data refresh with loading indicators

### Key Features to Include

#### 1. Performance Metrics
- **Sales Trends:** Daily, weekly, monthly comparison charts
- **Customer Analytics:** Acquisition, retention, and satisfaction rates
- **Revenue Analysis:** Profit margins, cost breakdowns, growth tracking
- **Operational Efficiency:** Service time, capacity utilization, peak hours
- **Comparative Benchmarks:** Performance against similar businesses

#### 2. Impact Reporting
- **Social Metrics:** Meals provided, families helped, community impact
- **Beneficiary Data:** Demographics, feedback, success stories
- **Geographic Reach:** Distribution maps and coverage analysis
- **Cost Effectiveness:** Impact per dollar spent analysis
- **Long-term Outcomes:** Sustained benefits and community development

#### 3. Comparative Analysis
- **Benchmarking:** Performance against similar tandoors or donors
- **Historical Comparison:** Year-over-year and period-over-period analysis
- **Goal Tracking:** Progress indicators with target achievement
- **Predictive Analytics:** Trend forecasting and future planning
- **ROI Analysis:** Return on investment for donors and business owners

#### 4. Export & Sharing
- **PDF Reports:** Professional formatted reports with branding
- **CSV Export:** Raw data for external analysis and record keeping
- **Social Sharing:** Impact story sharing for social media
- **Email Reports:** Scheduled delivery with customizable frequency
- **Print Options:** Printer-friendly layouts for physical records

### Design Considerations
- **Color Accessibility:** Color-blind friendly chart color schemes
- **Responsive Charts:** Mobile-optimized visualization sizing
- **Alternative Text:** Screen reader descriptions for all charts
- **Data Transparency:** Source attribution and timestamp display
- **Quick Analysis:** Both summary views and detailed exploration options

---

## Design Prompt 10: Notification & Communication Screens

### Screen Category
**Notification Center & Messaging**

### Target User Type
All users

### Primary Use Case
System notifications, alerts, user communication, and support interactions

### Detailed Requirements

#### Layout Specifications
- **Notification Header:** 80px with unread count and mark-all-read option
- **Categorized List:** Grouped notifications with visual section dividers
- **Message Threads:** Support communication with conversation layout
- **Quick Actions:** Swipe actions and bulk selection controls
- **Settings Access:** Direct link to notification preference management

#### Notification Types
- **Transaction Alerts:** Purchase confirmations with receipt access
- **System Messages:** App updates, maintenance, and service notifications
- **Security Alerts:** Login attempts, suspicious activity, account changes
- **Promotional Content:** Feature announcements with opt-out options
- **Social Updates:** Community messages and peer interactions

#### Visual Hierarchy
- **Unread Emphasis:** Bold text and colored indicators for new items
- **Time Stamps:** Relative formatting (2 hours ago, yesterday)
- **Category Icons:** Visual identification for quick scanning
- **Priority Levels:** Color coding for urgency (red, yellow, green)
- **Expandable Content:** Detailed information with tap-to-expand

### Key Features to Include

#### 1. Notification Management
- **Swipe Actions:** Mark as read, delete, archive with gesture controls
- **Bulk Selection:** Multi-select for batch operations
- **Search Function:** Find specific notifications with keyword search
- **Archive System:** Important notification storage and retrieval
- **Filter Options:** Category, date, and priority filtering

#### 2. Real-Time Updates
- **Push Integration:** Sound, vibration, and visual notification delivery
- **In-App Banners:** Immediate alerts for urgent information
- **Badge Counts:** Unread indicators on app icon and navigation
- **Background Sync:** Offline message queuing and delivery
- **Priority Routing:** Emergency notifications with immediate display

#### 3. Communication Tools
- **Support Messaging:** Direct communication with customer service
- **Community Updates:** Announcements and system-wide messages
- **Feedback Submission:** Bug reports and suggestions with attachments
- **Emergency Contacts:** Priority routing for urgent assistance
- **Multi-Language:** Notification templates in user's preferred language

#### 4. Customization Options
- **Frequency Controls:** Notification timing and batching preferences
- **Do Not Disturb:** Scheduled quiet hours with emergency overrides
- **Sound Preferences:** Custom tones and vibration patterns
- **Channel Management:** Granular control over notification types
- **Language Settings:** Notification language independent of app language

### Design Considerations
- **Urgency Hierarchy:** Clear visual distinction for priority levels
- **Consistent Iconography:** Standardized symbols across notification types
- **Settings Access:** Easy path to notification preference management
- **Offline Queuing:** Reliable delivery when connectivity is restored
- **Accessibility Support:** Screen reader compatibility and voice notifications

---

## Conclusion

These comprehensive Figma design prompts provide detailed specifications for creating a complete, accessible, and culturally sensitive mobile application for the Roti Meharbaan ecosystem. Each prompt addresses the unique needs of the three user types while maintaining consistency in design language and user experience.

### Key Design Principles Summary
- **Accessibility First:** Large touch targets, high contrast, simple navigation
- **Cultural Sensitivity:** Islamic colors, local context, respectful imagery
- **Multi-Language Support:** Seamless English/Urdu switching with RTL support
- **Trust & Security:** Clear security indicators and transparent processes
- **Social Impact Focus:** Emphasis on community benefit and positive outcomes

### Implementation Guidelines
1. **Start with User Registration:** Establish the foundation for user onboarding
2. **Prioritize Core Functionality:** QR scanning and dashboard screens first
3. **Iterate with User Testing:** Regular feedback from actual target users
4. **Maintain Consistency:** Use established design system throughout
5. **Plan for Scalability:** Design components that can grow with the platform

This document serves as a comprehensive guide for creating pixel-perfect Figma mockups that will effectively serve the Roti Meharbaan community and support the mission of providing affordable nutrition to laborers in Pakistan.
