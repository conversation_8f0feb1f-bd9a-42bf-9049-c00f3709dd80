Set objFSO = CreateObject("Scripting.FileSystemObject")
Set objShell = CreateObject("Shell.Application")

' Check if file exists
docxFile = "Roti Mehar<PERSON>an - Product Requirements Document (PRD).docx"
If objFSO.FileExists(docxFile) Then
    ' Create log file
    Set logFile = objFSO.CreateTextFile("vbs_log.txt", True)
    logFile.WriteLine "Starting VBS extraction..."
    logFile.WriteLine "Found file: " & docxFile
    
    ' Try to extract using Shell.Application
    On Error Resume Next
    
    ' Create temp folder
    tempFolder = "temp_extract"
    If objFSO.FolderExists(tempFolder) Then
        objFSO.DeleteFolder tempFolder, True
    End If
    objFSO.CreateFolder tempFolder
    
    ' Copy docx to zip
    zipFile = tempFolder & "\document.zip"
    objFSO.CopyFile docxFile, zipFile
    
    ' Extract zip
    Set objShell = CreateObject("Shell.Application")
    Set zipFolder = objShell.NameSpace(objFSO.GetAbsolutePathName(zipFile))
    Set extractFolder = objShell.NameSpace(objFSO.GetAbsolutePathName(tempFolder))
    
    If Not zipFolder Is Nothing Then
        extractFolder.CopyHere zipFolder.Items, 4
        logFile.WriteLine "Extracted zip contents"
        
        ' Check for document.xml
        documentXml = tempFolder & "\word\document.xml"
        If objFSO.FileExists(documentXml) Then
            logFile.WriteLine "Found document.xml"
            
            ' Read XML file
            Set xmlFile = objFSO.OpenTextFile(documentXml, 1)
            xmlContent = xmlFile.ReadAll
            xmlFile.Close
            
            ' Simple text extraction (remove XML tags)
            text = xmlContent
            text = Replace(text, "<", vbCrLf & "<")
            text = Replace(text, ">", ">" & vbCrLf)
            
            ' Save extracted content
            Set outputFile = objFSO.CreateTextFile("extracted_content.txt", True)
            outputFile.WriteLine "=== EXTRACTED CONTENT ==="
            outputFile.WriteLine text
            outputFile.Close
            
            logFile.WriteLine "Content saved to extracted_content.txt"
        Else
            logFile.WriteLine "document.xml not found"
        End If
    Else
        logFile.WriteLine "Failed to open zip file"
    End If
    
    ' Clean up
    If objFSO.FolderExists(tempFolder) Then
        objFSO.DeleteFolder tempFolder, True
    End If
    
    logFile.WriteLine "VBS extraction completed"
    logFile.Close
    
    ' Create success indicator
    Set successFile = objFSO.CreateTextFile("vbs_success.txt", True)
    successFile.WriteLine "VBS script completed"
    successFile.Close
Else
    ' Create error file
    Set errorFile = objFSO.CreateTextFile("vbs_error.txt", True)
    errorFile.WriteLine "File not found: " & docxFile
    errorFile.Close
End If
