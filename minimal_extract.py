import zipfile
import xml.etree.ElementTree as ET

# Extract content from DOCX
try:
    with zipfile.ZipFile("Roti Meharbaan - Product Requirements Document (PRD).docx", 'r') as docx_zip:
        document_xml = docx_zip.read('word/document.xml')
        root = ET.fromstring(document_xml)
        
        # Extract all text
        content = []
        namespace = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}
        
        for para in root.findall('.//w:p', namespace):
            para_text = ""
            for text_elem in para.findall('.//w:t', namespace):
                if text_elem.text:
                    para_text += text_elem.text
            if para_text.strip():
                content.append(para_text.strip())
        
        # Save to file
        with open("content.txt", "w", encoding="utf-8") as f:
            f.write("\n".join(content))
            
        with open("success.txt", "w") as f:
            f.write("SUCCESS")
            
except Exception as e:
    with open("error.txt", "w") as f:
        f.write(str(e))
