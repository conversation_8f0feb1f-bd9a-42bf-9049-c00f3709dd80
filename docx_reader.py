#!/usr/bin/env python3
"""
DOCX Reader Script
Reads and extracts content from .docx files in the current directory
Uses multiple fallback methods to read docx files
"""

import os
import sys
import zipfile
import xml.etree.ElementTree as ET
from pathlib import Path

def try_python_docx():
    """Try to use python-docx library"""
    try:
        import docx
        return True, docx
    except ImportError:
        return False, None

def read_docx_with_zipfile(file_path):
    """Fallback method: Read docx as zip file and extract text from XML"""
    try:
        print(f"Using zipfile method to read: {file_path}")

        content = []

        with zipfile.ZipFile(file_path, 'r') as docx_zip:
            # Read the main document
            try:
                document_xml = docx_zip.read('word/document.xml')
                root = ET.fromstring(document_xml)

                # Define namespace
                namespace = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}

                # Extract text from paragraphs
                for para in root.findall('.//w:p', namespace):
                    para_text = ""
                    for text_elem in para.findall('.//w:t', namespace):
                        if text_elem.text:
                            para_text += text_elem.text

                    if para_text.strip():
                        content.append(para_text.strip())

                return "\n".join(content)

            except Exception as e:
                print(f"Error parsing XML: {e}")
                return None

    except Exception as e:
        print(f"Error reading with zipfile method: {e}")
        return None

def read_docx_file(file_path):
    """Read and extract content from a .docx file using available methods"""
    print(f"\n{'='*60}")
    print(f"Reading file: {file_path}")
    print(f"{'='*60}")

    # Try python-docx first
    has_docx, docx_module = try_python_docx()

    if has_docx:
        try:
            print("Using python-docx library...")
            doc = docx_module.Document(file_path)

            # Extract document content
            content = []

            # Read paragraphs
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:  # Only include non-empty paragraphs
                    # Check if this is a heading (based on style or formatting)
                    style_name = paragraph.style.name if paragraph.style else "Normal"

                    # Add formatting indicators for headings
                    if "Heading" in style_name:
                        level = style_name.replace("Heading ", "").strip()
                        if level.isdigit():
                            heading_marker = "#" * int(level)
                            content.append(f"\n{heading_marker} {text}\n")
                        else:
                            content.append(f"\n## {text}\n")
                    else:
                        content.append(text)

            # Read tables if any
            if doc.tables:
                content.append("\n\n--- TABLES ---\n")
                for table_idx, table in enumerate(doc.tables):
                    content.append(f"\nTable {table_idx + 1}:")
                    for row in table.rows:
                        row_data = []
                        for cell in row.cells:
                            cell_text = cell.text.strip()
                            row_data.append(cell_text)
                        if any(row_data):  # Only include non-empty rows
                            content.append(" | ".join(row_data))
                    content.append("")

            return "\n".join(content)

        except Exception as e:
            print(f"Error with python-docx: {e}")
            print("Falling back to zipfile method...")

    # Fallback to zipfile method
    return read_docx_with_zipfile(file_path)

def find_docx_files():
    """Find all .docx files in the current directory"""
    current_dir = Path(".")
    docx_files = list(current_dir.glob("*.docx"))
    
    # Filter out temporary files (starting with ~$)
    docx_files = [f for f in docx_files if not f.name.startswith("~$")]
    
    return docx_files

def main():
    """Main function to read all .docx files"""
    try:
        print("DOCX File Reader")
        print("================")

        # Find .docx files
        docx_files = find_docx_files()

        if not docx_files:
            print("No .docx files found in the current directory.")
            return

        print(f"\nFound {len(docx_files)} .docx file(s):")
        for file in docx_files:
            print(f"  - {file.name}")

        # Read each file
        all_content = {}
        for file_path in docx_files:
            print(f"\nProcessing: {file_path.name}")
            content = read_docx_file(file_path)
            if content:
                all_content[file_path.name] = content

                # Save content to a text file for easy reference
                output_file = file_path.with_suffix('.txt')
                try:
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"✓ Content saved to: {output_file}")
                except Exception as e:
                    print(f"✗ Failed to save content to {output_file}: {e}")
            else:
                print(f"✗ Failed to read content from {file_path.name}")

        # Display summary
        print(f"\n{'='*60}")
        print("SUMMARY")
        print(f"{'='*60}")

        for filename, content in all_content.items():
            print(f"\nFile: {filename}")
            print(f"Content length: {len(content)} characters")

            # Show first few lines as preview
            lines = content.split('\n')
            non_empty_lines = [line for line in lines if line.strip()]
            preview_lines = non_empty_lines[:5] if non_empty_lines else ["(Empty content)"]

            print("Preview:")
            for line in preview_lines:
                print(f"  {line[:100]}{'...' if len(line) > 100 else ''}")

            if len(non_empty_lines) > 5:
                print(f"  ... and {len(non_empty_lines) - 5} more lines")

    except Exception as e:
        print(f"Error in main function: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
