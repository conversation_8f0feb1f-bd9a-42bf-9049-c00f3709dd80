=== EXTRACTED CONTENT ===

<?xml version="1.0" encoding="UTF-8" standalone="yes"?>


<w:document xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml" xmlns:sl="http://schemas.openxmlformats.org/schemaLibrary/2006/main" xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture" xmlns:c="http://schemas.openxmlformats.org/drawingml/2006/chart" xmlns:lc="http://schemas.openxmlformats.org/drawingml/2006/lockedCanvas" xmlns:dgm="http://schemas.openxmlformats.org/drawingml/2006/diagram" xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape" xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup" xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml" xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml" xmlns:w16="http://schemas.microsoft.com/office/word/2018/wordml" xmlns:w16cex="http://schemas.microsoft.com/office/word/2018/wordml/cex" xmlns:w16cid="http://schemas.microsoft.com/office/word/2016/wordml/cid" xmlns="http://schemas.microsoft.com/office/tasks/2019/documenttasks" xmlns:cr="http://schemas.microsoft.com/office/comments/2020/reactions">

<w:body>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000001">

<w:pPr>

<w:pStyle w:val="Heading1"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="480" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="46"/>

<w:szCs w:val="46"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_y8rhn7snce1q" w:id="0"/>

<w:bookmarkEnd w:id="0"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="46"/>

<w:szCs w:val="46"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Roti Meharbaan - Product Requirements Document (PRD)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000002">

<w:pPr>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000003">

<w:pPr>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr/>

<w:drawing>

<wp:inline distB="114300" distT="114300" distL="114300" distR="114300">

<wp:extent cx="5248275" cy="1476375"/>

<wp:effectExtent b="0" l="0" r="0" t="0"/>

<wp:docPr id="1" name="image1.png"/>

<a:graphic>

<a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture">

<pic:pic>

<pic:nvPicPr>

<pic:cNvPr id="0" name="image1.png"/>

<pic:cNvPicPr preferRelativeResize="0"/>

</pic:nvPicPr>

<pic:blipFill>

<a:blip r:embed="rId6"/>

<a:srcRect b="0" l="0" r="0" t="0"/>

<a:stretch>

<a:fillRect/>

</a:stretch>

</pic:blipFill>

<pic:spPr>

<a:xfrm>

<a:off x="0" y="0"/>

<a:ext cx="5248275" cy="1476375"/>

</a:xfrm>

<a:prstGeom prst="rect"/>

<a:ln/>

</pic:spPr>

</pic:pic>

</a:graphicData>

</a:graphic>

</wp:inline>

</w:drawing>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000004">

<w:pPr>

<w:pStyle w:val="Heading2"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="80" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_qoydasyk8z06" w:id="1"/>

<w:bookmarkEnd w:id="1"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
📋 Document Information
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000005">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="41"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Product Name:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Roti
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Meharbaan
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 (
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="1"/>

</w:rPr>

<w:t xml:space="preserve">
روٹی
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="1"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="1"/>

</w:rPr>

<w:t xml:space="preserve">
مہربان
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000006">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="41"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Version:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 1.0
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000007">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="41"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Date:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 July 2025
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000008">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="41"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Document Type:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Mobile Application PRD
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000009">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="41"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Platform:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Android (Primary), iOS (Future)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000000A">

<w:pPr>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:pict>

<v:rect style="width:0.0pt;height:1.5pt" o:hr="t" o:hrstd="t" o:hralign="center" fillcolor="#A0A0A0" stroked="f"/>

</w:pict>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000000B">

<w:pPr>

<w:pStyle w:val="Heading2"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="80" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_nhs6r9l2ycrj" w:id="2"/>

<w:bookmarkEnd w:id="2"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
🎯 Product Overview
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000000C">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_f8kjnjgga8fb" w:id="3"/>

<w:bookmarkEnd w:id="3"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Mission Statement
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000000D">

<w:pPr>

<w:spacing w:after="240" w:before="240" w:lineRule="auto"/>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Roti Meharbaan is a social impact mobile application that provides subsidized bread (roti) to laborers (mazdoors) in Pakistan through a donor-funded ecosystem, connecting charitable donors with tandoor owners to ensure affordable nutrition for the working class.
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000000E">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_mix5edlxjdgk" w:id="4"/>

<w:bookmarkEnd w:id="4"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Problem Statement
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000000F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="18"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Laborers in Pakistan struggle to afford basic nutrition, with roti costing approximately 60 PKR for 3 pieces, with each laborer earning on average 30K PKR per month, this cost is around 30% to 40% of its total expense while staying away from his home. 
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000010">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="18"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Limited access to affordable food impacts worker productivity and family welfare
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000011">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="18"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Lack of structured donation systems for food assistance
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000012">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="18"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
No transparency in charitable food distribution
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000013">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_cdaabdvgnwm4" w:id="5"/>

<w:bookmarkEnd w:id="5"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Solution
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000014">

<w:pPr>

<w:spacing w:after="240" w:before="240" w:lineRule="auto"/>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
A three-sided marketplace connecting laborers, tandoor owners, and donors through a mobile application with QR code technology, geolocation services, and transparent payment systems.
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000015">

<w:pPr>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:pict>

<v:rect style="width:0.0pt;height:1.5pt" o:hr="t" o:hrstd="t" o:hralign="center" fillcolor="#A0A0A0" stroked="f"/>

</w:pict>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000016">

<w:pPr>

<w:pStyle w:val="Heading2"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="80" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_dn6cqd7mvdpc" w:id="6"/>

<w:bookmarkEnd w:id="6"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
👥 User Types &amp; Requirements
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000017">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_v6n0d5xpa26m" w:id="7"/>

<w:bookmarkEnd w:id="7"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
1. Mazdoor (Laborer) - Primary Beneficiary
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000018">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_7gilu3syn9hc" w:id="8"/>

<w:bookmarkEnd w:id="8"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
User Profile:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000019">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="3"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Demographics:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Blue-collar workers, construction workers, daily wage earners
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000001A">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="3"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Age Range:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 18-55 years
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000001B">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="3"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Income Level:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Low income (PKR 15,000-25,000/month)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000001C">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="3"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Education:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Limited formal education, basic smartphone literacy
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000001D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="3"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Languages:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Primarily Urdu, limited English
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000001E">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_crib4yvl6bl1" w:id="9"/>

<w:bookmarkEnd w:id="9"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Key Requirements:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000001F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="20"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Simple, intuitive interface with large buttons
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000020">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="20"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Bilingual support (Urdu primary, English secondary)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000021">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="20"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Minimal data usage
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000022">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="20"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Works on basic Android smartphones
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000023">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="20"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Clear visual indicators for remaining units
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000024">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="20"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Easy QR scanning functionality
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000025">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_gfizn2jjcnf5" w:id="10"/>

<w:bookmarkEnd w:id="10"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
User Goals:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000026">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="35"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Access affordable roti (3 pieces for 5 PKR instead of 60 PKR)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000027">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="35"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Track daily consumption limits (3 units maximum)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000028">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="35"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Find nearby registered tandoors
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000029">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="35"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Monitor monthly savings
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000002A">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_ba16ugbmmefm" w:id="11"/>

<w:bookmarkEnd w:id="11"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
2. Tandoor Owner - Service Provider
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000002B">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_4qua6koblwfv" w:id="12"/>

<w:bookmarkEnd w:id="12"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
User Profile:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000002C">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="32"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Demographics:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Tandoors located in housing societies or near residential areas. 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000002D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="32"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Business Type:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Traditional tandoor/bakery operations
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000002E">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="32"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Tech Literacy:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Moderate smartphone usage
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000002F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="32"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Languages:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Urdu, some English
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000030">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_two50hj1dx5s" w:id="13"/>

<w:bookmarkEnd w:id="13"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Key Requirements:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000031">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="50"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Business dashboard with daily analytics
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000032">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="50"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
QR code generation and management, with ability to change QR code print on Tandoor reception at will. 
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000033">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="50"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Location-based registration system
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000034">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="50"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Payment tracking and history
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000035">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="50"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Worker verification system
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000036">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_4d5pjhqeg84o" w:id="14"/>

<w:bookmarkEnd w:id="14"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
User Goals:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000037">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="5"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Increase customer footfall through subsidized program
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000038">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="5"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Track business metrics and revenue
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000039">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="5"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Manage daily roti distribution
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000003A">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="5"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Verify legitimate laborers
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000003B">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_6acozalqfvmc" w:id="15"/>

<w:bookmarkEnd w:id="15"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
3. Donor - Funding Source
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000003C">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_iqlzx6z7r7et" w:id="16"/>

<w:bookmarkEnd w:id="16"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
User Profile:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000003D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="8"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Demographics:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Middle to upper-middle class, philanthropists, corporate CSR
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000003E">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="8"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

<w:rPr>

<w:u w:val="none"/>

</w:rPr>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Scope
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
: Through Easypaisa or Debit/ Credit card secure transfer or Offline Account Tansfer
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000003F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="8"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Education:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Well-educated, tech-savvy
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="********">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="8"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Languages:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 English primary, Urdu
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000041">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_3xtzy0imhqdf" w:id="17"/>

<w:bookmarkEnd w:id="17"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Key Requirements:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000042">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="52"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Transparent impact reporting
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000043">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="52"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Multiple payment options
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000044">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="52"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Donation history and analytics
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000045">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="52"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Impact visualization
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000046">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="52"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Tax documentation support
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000047">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_d9mvgedys7qt" w:id="18"/>

<w:bookmarkEnd w:id="18"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
User Goals:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000048">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="2"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Make meaningful social impact
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000049">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="2"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Track donation effectiveness
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000004A">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="2"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Receive transparent reporting
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000004B">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="2"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Convenient donation process
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000004C">

<w:pPr>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:pict>

<v:rect style="width:0.0pt;height:1.5pt" o:hr="t" o:hrstd="t" o:hralign="center" fillcolor="#A0A0A0" stroked="f"/>

</w:pict>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000004D">

<w:pPr>

<w:pStyle w:val="Heading2"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="80" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_uwmkuetuohmw" w:id="19"/>

<w:bookmarkEnd w:id="19"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
💰 Business Model &amp; Pricing
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000004E">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_enxyks55cphi" w:id="20"/>

<w:bookmarkEnd w:id="20"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Transaction Flow:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000004F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="1"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Laborer Payment:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 5 PKR per unit (3 rotis)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000050">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="1"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Actual Cost:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 60 PKR per unit
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000051">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="1"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Donor Contribution:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 60 PKR per unit
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000052">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="1"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Platform Commission:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 5 PKR per transaction (from donor amount)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000053">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="1"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Tandoor Revenue:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 60 PKR per unit (from combined laborer + donor funding) transferred at the end of each day reconciled and aggregated for the whole day. 
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000054">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_txjwia9jrzd5" w:id="21"/>

<w:bookmarkEnd w:id="21"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Daily Limits:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000055">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="23"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Maximum Units per Laborer:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 3 units/day
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000056">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="23"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Unit Definition:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 1 unit = 3 rotis
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000057">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="23"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Daily Cost to Laborer:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Maximum 15 PKR (3 units × 5 PKR)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000058">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="23"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Daily Donor Impact:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Maximum 180 PKR per laborer (3 units × 60 PKR)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000059">

<w:pPr>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:pict>

<v:rect style="width:0.0pt;height:1.5pt" o:hr="t" o:hrstd="t" o:hralign="center" fillcolor="#A0A0A0" stroked="f"/>

</w:pict>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000005A">

<w:pPr>

<w:pStyle w:val="Heading2"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="80" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_dci0uolrhmr4" w:id="22"/>

<w:bookmarkEnd w:id="22"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
🔧 Core Features &amp; Functionality
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000005B">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_ouj7mr65gt5l" w:id="23"/>

<w:bookmarkEnd w:id="23"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Authentication &amp; Registration
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000005C">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_gcmgnafsiwtw" w:id="24"/>

<w:bookmarkEnd w:id="24"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Mazdoor Registration:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000005D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="49"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Required Fields:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000005E">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="49"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Full Name (Urdu/English)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000005F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="49"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Mobile Number (with OTP verification)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000060">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="49"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
CNIC Number - Optional 
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000061">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="49"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Work Location/Area
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000062">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="49"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Employment Type
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000063">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="49"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Verification Process:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000064">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="49"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
SMS OTP verification
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000065">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="49"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
CNIC validation
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000066">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="49"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Photo capture for profile
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000067">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="49"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Security Features:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000068">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="49"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Biometric authentication (fingerprint)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000069">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="49"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
PIN-based backup access
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000006A">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_6phcpq5oefzy" w:id="25"/>

<w:bookmarkEnd w:id="25"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Tandoor Registration:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000006B">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="47"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Required Fields:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000006C">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="47"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Business Name
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000006D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="47"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Owner Name
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000006E">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="47"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Business License Number
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000006F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="47"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Complete Address
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000070">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="47"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Contact Information
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000071">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="47"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Location Verification:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000072">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="47"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
GPS coordinates capture
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000073">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="47"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Google Maps integration
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000074">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="47"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Address verification
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000075">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="47"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Business Verification:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000076">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="47"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
License document upload
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000077">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="47"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Business photos
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000078">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="47"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Admin approval process
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000079">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_qjfox7sydvvp" w:id="26"/>

<w:bookmarkEnd w:id="26"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Donor Registration:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000007A">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="43"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Required Fields:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000007B">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="43"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Full Name
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000007C">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="43"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Email Address
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000007D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="43"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Phone Number
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000007E">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="43"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Monthly Donation Goal (optional)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000007F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="43"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Payment Setup:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000080">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="43"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Credit/Debit card linking
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000081">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="43"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Mobile wallet integration (JazzCash, EasyPaisa)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="********">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="43"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Bank account connection
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="********">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_1dfkys4azcwk" w:id="27"/>

<w:bookmarkEnd w:id="27"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Core Application Features
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="********">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_gw9m4jjvpvgd" w:id="28"/>

<w:bookmarkEnd w:id="28"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
QR Code System:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000085">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="40"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
QR Code Generation:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000086">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="40"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Unique daily QR codes for each tandoor
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000087">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="40"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Time-based expiration (24 hours)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000088">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="40"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Encrypted transaction data
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000089">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="40"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
QR Code Scanning:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000008A">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="40"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Camera-based scanning
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000008B">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="40"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Manual code entry option
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000008C">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="40"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Offline verification capability
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000008D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="40"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Real-time validation
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000008E">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_jg7y2n9wn4t2" w:id="29"/>

<w:bookmarkEnd w:id="29"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Geolocation Services:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000008F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="10"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Tandoor Discovery:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000090">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="10"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Map-based tandoor finder
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000091">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="10"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Distance calculation
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000092">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="10"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Operating hours display
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000093">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="10"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Location Verification:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000094">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="10"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
GPS-based check-in
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000095">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="10"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Geo-fencing for valid transactions
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000096">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="10"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Location history tracking
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000097">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_xzu0yxn7dypq" w:id="30"/>

<w:bookmarkEnd w:id="30"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Payment Processing:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000098">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="22"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Laborer Payments:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000099">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="22"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Mobile wallet integration
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000009A">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="22"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Cash payment recording
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000009B">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="22"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Payment history
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000009C">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="22"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Donor Transactions:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000009D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="22"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Automated donation processing
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000009E">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="22"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Subscription-based giving
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000009F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="22"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
One-time donations
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000A0">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="22"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Payment failure handling
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000A1">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_a6tmpxhbapl0" w:id="31"/>

<w:bookmarkEnd w:id="31"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Daily Consumption Tracking:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000A2">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="15"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Unit Management:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000A3">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="15"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Real-time unit consumption tracking
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000A4">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="15"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Daily limit enforcement
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000A5">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="15"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Reset mechanism (daily at midnight)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000A6">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="15"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
History &amp; Analytics:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000A7">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="15"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Monthly consumption reports
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000A8">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="15"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Savings calculation
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000A9">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="15"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Nutrition impact metrics
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000AA">

<w:pPr>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:pict>

<v:rect style="width:0.0pt;height:1.5pt" o:hr="t" o:hrstd="t" o:hralign="center" fillcolor="#A0A0A0" stroked="f"/>

</w:pict>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000AB">

<w:pPr>

<w:pStyle w:val="Heading2"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="80" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_pdoah917zkbz" w:id="32"/>

<w:bookmarkEnd w:id="32"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
🎨 Design Guidelines &amp; Brand Identity
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000AC">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_vxtto4wcicuw" w:id="33"/>

<w:bookmarkEnd w:id="33"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Logo Design Specifications
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000AD">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_ug8ge0jw5t7y" w:id="34"/>

<w:bookmarkEnd w:id="34"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Primary Logo:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000AE">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="29"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Text:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 &quot;ROTI&quot; in bold English letters
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000AF">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="29"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Special Element:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Traditional roti graphic embedded in the &quot;O&quot;
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000B0">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="29"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Urdu Text:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 &quot;
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="1"/>

</w:rPr>

<w:t xml:space="preserve">
مہربان
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
&quot; (
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Meharbaan
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
) 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
in
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Noto
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Nastaliq
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Urdu
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
font
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000B1">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="29"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Logo Variations:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000B2">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="29"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Horizontal layout (primary)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000B3">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="29"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Stacked layout (mobile)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000B4">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="29"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Icon-only version (app icon)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000B5">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_a38njc3rfle1" w:id="35"/>

<w:bookmarkEnd w:id="35"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Roti Graphic Details:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000B6">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="53"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Design:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Circular roti with traditional texture patterns
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000B7">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="53"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Colors:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Gradient from light beige center to darker beige edges
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000B8">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="53"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Effects:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Subtle shadow and texture to simulate real roti
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000B9">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="53"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Dimensions:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Fits perfectly in the &quot;O&quot; letter space
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000BA">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_ddxdrsms869c" w:id="36"/>

<w:bookmarkEnd w:id="36"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Color Palette
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000BB">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_op6ogqxzb0c0" w:id="37"/>

<w:bookmarkEnd w:id="37"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Primary Colors:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000BC">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="14"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Beige (Roti Color):
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rFonts w:ascii="Roboto Mono" w:cs="Roboto Mono" w:eastAsia="Roboto Mono" w:hAnsi="Roboto Mono"/>

<w:color w:val="188038"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
#F5DEB3
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 (Light), 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rFonts w:ascii="Roboto Mono" w:cs="Roboto Mono" w:eastAsia="Roboto Mono" w:hAnsi="Roboto Mono"/>

<w:color w:val="188038"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
#DEB887
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 (Medium)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000BD">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="14"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Maroon (Accent):
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rFonts w:ascii="Roboto Mono" w:cs="Roboto Mono" w:eastAsia="Roboto Mono" w:hAnsi="Roboto Mono"/>

<w:color w:val="188038"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
#800020
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 (Primary), 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rFonts w:ascii="Roboto Mono" w:cs="Roboto Mono" w:eastAsia="Roboto Mono" w:hAnsi="Roboto Mono"/>

<w:color w:val="188038"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
#A0293D
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 (Secondary)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000BE">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="14"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Background:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Gradient from 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rFonts w:ascii="Roboto Mono" w:cs="Roboto Mono" w:eastAsia="Roboto Mono" w:hAnsi="Roboto Mono"/>

<w:color w:val="188038"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
#800020
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 to 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rFonts w:ascii="Roboto Mono" w:cs="Roboto Mono" w:eastAsia="Roboto Mono" w:hAnsi="Roboto Mono"/>

<w:color w:val="188038"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
#F5DEB3
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000BF">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_9zjjqjsdi1u3" w:id="38"/>

<w:bookmarkEnd w:id="38"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Secondary Colors:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000C0">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="51"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Success:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rFonts w:ascii="Roboto Mono" w:cs="Roboto Mono" w:eastAsia="Roboto Mono" w:hAnsi="Roboto Mono"/>

<w:color w:val="188038"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
#28A745
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 (transaction confirmations)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000C1">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="51"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Warning:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rFonts w:ascii="Roboto Mono" w:cs="Roboto Mono" w:eastAsia="Roboto Mono" w:hAnsi="Roboto Mono"/>

<w:color w:val="188038"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
#FFC107
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 (alerts and notifications)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000C2">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="51"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Error:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rFonts w:ascii="Roboto Mono" w:cs="Roboto Mono" w:eastAsia="Roboto Mono" w:hAnsi="Roboto Mono"/>

<w:color w:val="188038"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
#DC3545
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 (error states)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000C3">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="51"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Info:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rFonts w:ascii="Roboto Mono" w:cs="Roboto Mono" w:eastAsia="Roboto Mono" w:hAnsi="Roboto Mono"/>

<w:color w:val="188038"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
#17A2B8
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 (informational content)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000C4">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_41fbuxj4nofg" w:id="39"/>

<w:bookmarkEnd w:id="39"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Neutral Colors:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000C5">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="30"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
White:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rFonts w:ascii="Roboto Mono" w:cs="Roboto Mono" w:eastAsia="Roboto Mono" w:hAnsi="Roboto Mono"/>

<w:color w:val="188038"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
#FFFFFF
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 (card backgrounds)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000C6">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="30"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Light Gray:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rFonts w:ascii="Roboto Mono" w:cs="Roboto Mono" w:eastAsia="Roboto Mono" w:hAnsi="Roboto Mono"/>

<w:color w:val="188038"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
#F8F9FA
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 (form backgrounds)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000C7">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="30"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Medium Gray:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rFonts w:ascii="Roboto Mono" w:cs="Roboto Mono" w:eastAsia="Roboto Mono" w:hAnsi="Roboto Mono"/>

<w:color w:val="188038"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
#6C757D
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 (secondary text)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000C8">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="30"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Dark Gray:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rFonts w:ascii="Roboto Mono" w:cs="Roboto Mono" w:eastAsia="Roboto Mono" w:hAnsi="Roboto Mono"/>

<w:color w:val="188038"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
#343A40
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 (primary text)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000C9">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_x5mrz6sqyg1j" w:id="40"/>

<w:bookmarkEnd w:id="40"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Typography
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000CA">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_ri1olntx4rx6" w:id="41"/>

<w:bookmarkEnd w:id="41"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Font Hierarchy:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000CB">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="38"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Primary Font:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Segoe UI (English text)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000CC">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="38"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Urdu Font:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Noto Nastaliq Urdu (Urdu text)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000CD">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="38"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Headings:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Bold, 24-32px
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000CE">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="38"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Body Text:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Regular, 16-18px
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000CF">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="38"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Captions:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Regular, 12-14px
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000D0">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_13z2bt23ayka" w:id="42"/>

<w:bookmarkEnd w:id="42"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Text Treatment:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000D1">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="33"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Headers:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Beige background with maroon text
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000D2">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="33"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Buttons:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 White text on maroon background
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000D3">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="33"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Form Labels:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Maroon color for emphasis
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000D4">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="33"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Success Messages:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Green text
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000D5">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="33"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Error Messages:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Red text
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000D6">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_27ovocg25aon" w:id="43"/>

<w:bookmarkEnd w:id="43"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Visual Elements
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000D7">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_q8hg4tm5rhso" w:id="44"/>

<w:bookmarkEnd w:id="44"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Cards &amp; Components:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000D8">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="4"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Border Radius:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 18px for cards, 12px for buttons
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000D9">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="4"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Shadows:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Multiple layer shadows for depth
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000DA">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="4"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Spacing:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 20px base unit for consistent spacing
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000DB">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="4"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Icons:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Simple line icons with maroon color
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000DC">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_hqiz501q2hjd" w:id="45"/>

<w:bookmarkEnd w:id="45"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Button Styles:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000DD">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="46"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Primary Button:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Maroon gradient with white text
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000DE">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="46"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Secondary Button:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Beige background with maroon text
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000DF">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="46"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Outline Button:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Transparent with maroon border
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000E0">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="46"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Disabled Button:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Gray with reduced opacity
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000E1">

<w:pPr>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:pict>

<v:rect style="width:0.0pt;height:1.5pt" o:hr="t" o:hrstd="t" o:hralign="center" fillcolor="#A0A0A0" stroked="f"/>

</w:pict>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000E2">

<w:pPr>

<w:pStyle w:val="Heading2"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="80" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_bo645uj7hafy" w:id="46"/>

<w:bookmarkEnd w:id="46"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
📱 Screen Specifications
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000E3">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_vw6gbb9xczou" w:id="47"/>

<w:bookmarkEnd w:id="47"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Mazdoor (Laborer) User Flow
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000E4">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_eg9ofaax3un9" w:id="48"/>

<w:bookmarkEnd w:id="48"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
1. Registration Screen
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000E5">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="25"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Components:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000E6">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="25"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
App logo at top
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000E7">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="25"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Form fields (Name, Phone, CNIC, Work Location)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000E8">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="25"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Primary
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
CTA
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
button
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 &quot;
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="1"/>

</w:rPr>

<w:t xml:space="preserve">
رجسٹر
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="1"/>

</w:rPr>

<w:t xml:space="preserve">
 
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="1"/>

</w:rPr>

<w:t xml:space="preserve">
کریں
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
&quot; (
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Register
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000E9">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="25"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Login link for existing users
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000EA">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="25"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Validations:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000EB">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="25"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
CNIC format validation
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000EC">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="25"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Phone number format
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000ED">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="25"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Required field checks
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000EE">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="25"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Languages:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Urdu primary with English support
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000EF">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_nu29v5wpb3d9" w:id="49"/>

<w:bookmarkEnd w:id="49"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
2. Dashboard Screen
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000F0">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="54"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Header:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000F1">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="54"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Beige background with user name
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000F2">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="54"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Remaining units counter (prominent display)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000F3">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="54"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Main Content:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000F4">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="54"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Daily unit status (2/3 remaining)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000F5">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="54"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
&quot;Get Roti&quot; action card
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000F6">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="54"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Registered tandoor information
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000F7">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="54"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Monthly statistics
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000F8">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="54"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Navigation:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Bottom tab bar (Home, History, Profile)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000F9">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_m3oman7489ia" w:id="50"/>

<w:bookmarkEnd w:id="50"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
3. QR Scanner Screen
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000FA">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="42"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Components:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000FB">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="42"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Camera viewfinder with overlay
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000FC">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="42"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Scanning animation
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000FD">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="42"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Manual code entry option
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000FE">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="42"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Instructions text
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000000FF">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="42"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Features:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000100">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="42"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Auto-focus camera
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000101">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="42"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Flashlight toggle
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000102">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="42"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
QR code validation
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000103">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="42"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Success/error feedback
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000104">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_sjs0w4e6czf" w:id="51"/>

<w:bookmarkEnd w:id="51"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
4. Transaction Confirmation
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000105">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="11"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Display:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000106">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="11"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Transaction details
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000107">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="11"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Remaining units after purchase
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000108">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="11"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Tandoor information
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000109">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="11"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Payment confirmation
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000010A">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="11"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Actions:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000010B">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="11"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Return to dashboard
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000010C">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="11"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
View receipt
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000010D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="11"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Rate experience
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000010E">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_p88nf6ttbl71" w:id="52"/>

<w:bookmarkEnd w:id="52"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Tandoor Owner User Flow
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000010F">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_88anhw7gfyvu" w:id="53"/>

<w:bookmarkEnd w:id="53"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
1. Business Dashboard
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000110">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="45"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Header:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Business name and location
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000111">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="45"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Analytics Cards:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000112">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="45"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Today's worker count
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000113">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="45"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Units sold today
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000114">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="45"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Daily revenue
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000115">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="45"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
QR code status
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000116">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="45"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Actions:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000117">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="45"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Generate new QR code
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000118">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="45"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
View transaction history
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000119">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="45"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Update business info
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000011A">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_6y0ecypl0kye" w:id="54"/>

<w:bookmarkEnd w:id="54"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
2. QR Code Management
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000011B">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="24"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Display:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000011C">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="24"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Current active QR code
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000011D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="24"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
QR code expiration time
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000011E">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="24"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Generation history
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000011F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="24"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Actions:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000120">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="24"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Generate new code
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000121">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="24"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Print QR code
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000122">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="24"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Share QR code
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000123">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_ev283dsbrunl" w:id="55"/>

<w:bookmarkEnd w:id="55"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
3. Transaction History
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000124">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="26"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
List View:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000125">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="26"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Transaction time stamps
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000126">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="26"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Worker names (if available)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000127">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="26"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Unit quantities
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000128">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="26"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Revenue amounts
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000129">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="26"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Filters:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000012A">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="26"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Date range
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000012B">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="26"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Transaction type
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000012C">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="26"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Worker search
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000012D">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_jgqxajodjup" w:id="56"/>

<w:bookmarkEnd w:id="56"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Donor User Flow
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000012E">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_vodw06wpbny5" w:id="57"/>

<w:bookmarkEnd w:id="57"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
1. Impact Dashboard
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000012F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="27"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Header:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Donor name and tier status
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000130">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="27"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Impact Metrics:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000131">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="27"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Monthly donation amount
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000132">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="27"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Workers helped
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000133">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="27"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Total meals provided
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000134">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="27"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Savings generated for workers
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000135">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="27"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Quick Actions:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000136">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="27"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Donation amount buttons
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000137">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="27"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Custom amount input
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000138">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="27"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Subscription management
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000139">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_xe3ub3m2olpq" w:id="58"/>

<w:bookmarkEnd w:id="58"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
2. Donation Screen
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000013A">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="6"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Amount Selection:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000013B">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="6"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Preset amounts (60, 300, 600 PKR)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000013C">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="6"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Custom amount input
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000013D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="6"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Impact preview (X meals, Y workers)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000013E">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="6"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Payment Methods:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000013F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="6"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Credit/Debit cards
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000140">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="6"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Mobile wallets
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="********">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="6"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Bank transfer
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="********">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="6"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Subscription Options:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="********">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="6"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
One-time donation
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000144">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="6"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Monthly recurring
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000145">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="6"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Custom frequency
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000146">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_zigc6mnneswm" w:id="59"/>

<w:bookmarkEnd w:id="59"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
3. Impact Reports
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000147">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="36"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Visual Analytics:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000148">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="36"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Donation history charts
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000149">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="36"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Geographic impact map
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000014A">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="36"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Worker benefit statistics
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000014B">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="36"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Detailed Reports:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000014C">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="36"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Monthly summaries
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000014D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="36"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Individual transaction details
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000014E">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="36"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Tax documentation
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000014F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="1"/>

<w:numId w:val="36"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="1440" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Social impact metrics
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000150">

<w:pPr>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:pict>

<v:rect style="width:0.0pt;height:1.5pt" o:hr="t" o:hrstd="t" o:hralign="center" fillcolor="#A0A0A0" stroked="f"/>

</w:pict>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000151">

<w:pPr>

<w:pStyle w:val="Heading2"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="80" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_vu3gq0qdyl2o" w:id="60"/>

<w:bookmarkEnd w:id="60"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
🔧 Technical Requirements
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000152">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_aih6dmnja07" w:id="61"/>

<w:bookmarkEnd w:id="61"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Platform Specifications
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000153">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="21"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Primary Platform:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Android (API Level 21+)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000154">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="21"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Secondary Platform:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 iOS (Future release)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000155">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="21"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Backend:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Cloud-based REST API
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000156">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="21"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Database:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Real-time database for live updates
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000157">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="21"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Payment Gateway:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Local Pakistani payment processors
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000158">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_pwdycunqow0a" w:id="62"/>

<w:bookmarkEnd w:id="62"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Performance Requirements
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000159">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="19"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
App Size:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 &lt; 50 MB
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000015A">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="19"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Load Time:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 &lt; 3 seconds on 3G connection
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000015B">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="19"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Offline Capability:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Basic functionality without internet
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000015C">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="19"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Battery Optimization:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Minimal background processing
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000015D">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_zejm6e87mbjn" w:id="63"/>

<w:bookmarkEnd w:id="63"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Security Requirements
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000015E">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="34"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Data Encryption:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 End-to-end encryption for sensitive data
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000015F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="34"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Authentication:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Multi-factor authentication for all users
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000160">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="34"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Payment Security:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 PCI DSS compliance
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000161">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="34"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Privacy:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 GDPR-compliant data handling
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000162">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_jaf396kviqtx" w:id="64"/>

<w:bookmarkEnd w:id="64"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Integration Requirements
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000163">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="13"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Maps:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Google Maps SDK for location services
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000164">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="13"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Payments:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 JazzCash, EasyPaisa, bank APIs
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="********">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="13"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
SMS:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 SMS gateway for OTP verification
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="********">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="13"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Analytics:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Application performance monitoring
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000167">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="13"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Push Notifications:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Real-time alerts and updates
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000168">

<w:pPr>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:pict>

<v:rect style="width:0.0pt;height:1.5pt" o:hr="t" o:hrstd="t" o:hralign="center" fillcolor="#A0A0A0" stroked="f"/>

</w:pict>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000169">

<w:pPr>

<w:pStyle w:val="Heading2"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="80" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_kqb96t60lw7a" w:id="65"/>

<w:bookmarkEnd w:id="65"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
📊 Success Metrics &amp; KPIs
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000016A">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_b4tr5u1wo9vl" w:id="66"/>

<w:bookmarkEnd w:id="66"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
User Adoption Metrics
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000016B">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="28"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Monthly Active Users (MAU):
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Target 10,000 laborers in first 6 months
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000016C">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="28"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Tandoor Network:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 500+ registered tandoors
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000016D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="28"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Donor Base:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 1,000+ active donors
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000016E">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_q5qv2wrn8krf" w:id="67"/>

<w:bookmarkEnd w:id="67"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Transaction Metrics
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000016F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="12"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Daily Transactions:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 Average 5,000 roti units/day
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000170">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="12"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Transaction Success Rate:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 &gt;98%
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000171">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="12"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Average Transaction Time:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 &lt;30 seconds
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000172">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_csir1tsyoxg5" w:id="68"/>

<w:bookmarkEnd w:id="68"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Social Impact Metrics
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000173">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="17"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Monthly Savings for Workers:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 PKR 500+ per laborer
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000174">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="17"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Meals Provided:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 100,000+ subsidized meals/month
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000175">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="17"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Geographic Coverage:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 5+ major Pakistani cities
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000176">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_bzsytrdz1kf0" w:id="69"/>

<w:bookmarkEnd w:id="69"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Business Metrics
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000177">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="9"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Revenue:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 PKR 500,000+ monthly commission
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000178">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="9"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Customer Retention:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 80%+ monthly retention rate
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000179">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="9"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Donor Engagement:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 70%+ monthly donation frequency
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000017A">

<w:pPr>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:pict>

<v:rect style="width:0.0pt;height:1.5pt" o:hr="t" o:hrstd="t" o:hralign="center" fillcolor="#A0A0A0" stroked="f"/>

</w:pict>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000017B">

<w:pPr>

<w:pStyle w:val="Heading2"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="80" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_hggbijeg7tji" w:id="70"/>

<w:bookmarkEnd w:id="70"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
🚀 Implementation Timeline
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000017C">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_o3sgpg85vkwd" w:id="71"/>

<w:bookmarkEnd w:id="71"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Phase 1: MVP Development (3 months)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000017D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="16"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Basic user registration and authentication
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000017E">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="16"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
QR code scanning and generation
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000017F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="16"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Core transaction processing
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000180">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="16"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Simple dashboard for all user types
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000181">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_ww3kls74tkok" w:id="72"/>

<w:bookmarkEnd w:id="72"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Phase 2: Enhanced Features (2 months)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000182">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="7"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Advanced analytics and reporting
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000183">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="7"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Payment gateway integration
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000184">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="7"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Geolocation services
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000185">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="7"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Push notifications
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000186">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_9vogr73uwy2x" w:id="73"/>

<w:bookmarkEnd w:id="73"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Phase 3: Scale &amp; Optimization (1 month)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000187">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="37"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Performance optimization
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000188">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="37"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Advanced security features
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000189">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="37"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Multi-language support
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000018A">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="37"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
iOS development initiation
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000018B">

<w:pPr>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:pict>

<v:rect style="width:0.0pt;height:1.5pt" o:hr="t" o:hrstd="t" o:hralign="center" fillcolor="#A0A0A0" stroked="f"/>

</w:pict>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000018C">

<w:pPr>

<w:pStyle w:val="Heading2"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="80" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_olph9haeh17z" w:id="74"/>

<w:bookmarkEnd w:id="74"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:sz w:val="34"/>

<w:szCs w:val="34"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
📋 Appendix
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000018D">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_o7dd2xqw0e3c" w:id="75"/>

<w:bookmarkEnd w:id="75"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Figma Design Requirements
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000018E">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_j6kde1l9zwyp" w:id="76"/>

<w:bookmarkEnd w:id="76"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Artboard Specifications:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000018F">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="39"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Screen Size:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 375×812px (iPhone X/11 Pro standard)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000190">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="39"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Android Adaptation:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 360×740px
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000191">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="39"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Tablet Version:
</w:t>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
 768×1024px (future consideration)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000192">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_wsexwmlxfmh8" w:id="77"/>

<w:bookmarkEnd w:id="77"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Component Library Required:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000193">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="48"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Button variants (primary, secondary, outline, disabled)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000194">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="48"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Form input fields with validation states
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000195">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="48"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Card components with different content types
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000196">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="48"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Navigation bars (top and bottom)
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000197">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="48"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Loading states and progress indicators
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000198">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="48"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Modal dialogs and alerts
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="00000199">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="48"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
QR code scanner interface mockup
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000019A">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_ah88fkey242a" w:id="78"/>

<w:bookmarkEnd w:id="78"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Flow Diagrams Needed:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000019B">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="55"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Complete user registration flows for all three user types
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000019C">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="55"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Transaction flow from QR scan to completion
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000019D">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="55"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Donation flow from amount selection to confirmation
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000019E">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="55"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Error handling and edge case scenarios
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="0000019F">

<w:pPr>

<w:pStyle w:val="Heading4"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:after="40" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_gujowwgkvqc8" w:id="79"/>

<w:bookmarkEnd w:id="79"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="22"/>

<w:szCs w:val="22"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Asset Requirements:
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001A0">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="31"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
High-resolution logo in multiple formats
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001A1">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="31"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Icon set for navigation and actions
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001A2">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="31"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Illustration style guide
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001A3">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="31"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Photo placeholders for user profiles and tandoor images
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001A4">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="31"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
QR code design templates
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001A5">

<w:pPr>

<w:pStyle w:val="Heading3"/>

<w:keepNext w:val="0"/>

<w:keepLines w:val="0"/>

<w:spacing w:before="280" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

</w:rPr>

</w:pPr>

<w:bookmarkStart w:colFirst="0" w:colLast="0" w:name="_ytz960fhi4ct" w:id="80"/>

<w:bookmarkEnd w:id="80"/>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:color w:val="000000"/>

<w:sz w:val="26"/>

<w:szCs w:val="26"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Content Requirements
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001A6">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="44"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="240" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Complete Urdu translations for all interface text
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001A7">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="44"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Error messages in both languages
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001A8">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="44"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Help documentation and FAQs
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001A9">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="44"/>

</w:numPr>

<w:spacing w:after="0" w:afterAutospacing="0" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Terms of service and privacy policy content
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001AA">

<w:pPr>

<w:numPr>

<w:ilvl w:val="0"/>

<w:numId w:val="44"/>

</w:numPr>

<w:spacing w:after="240" w:before="0" w:beforeAutospacing="0" w:lineRule="auto"/>

<w:ind w:left="720" w:hanging="360"/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Push notification message templates
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001AB">

<w:pPr>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:pict>

<v:rect style="width:0.0pt;height:1.5pt" o:hr="t" o:hrstd="t" o:hralign="center" fillcolor="#A0A0A0" stroked="f"/>

</w:pict>

</w:r>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001AC">

<w:pPr>

<w:spacing w:after="240" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:b w:val="1"/>

</w:rPr>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:b w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
Document End
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001AD">

<w:pPr>

<w:spacing w:after="240" w:before="240" w:lineRule="auto"/>

<w:rPr>

<w:i w:val="1"/>

</w:rPr>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:i w:val="1"/>

<w:rtl w:val="0"/>

</w:rPr>

<w:t xml:space="preserve">
This PRD serves as the comprehensive guide for Figma design creation and development implementation of the Roti Meharbaan mobile application.
</w:t>

</w:r>

</w:p>

<w:p w:rsidR="********" w:rsidDel="********" w:rsidP="********" w:rsidRDefault="********" w:rsidRPr="********" w14:paraId="000001AE">

<w:pPr>

<w:rPr/>

</w:pPr>

<w:r w:rsidDel="********" w:rsidR="********" w:rsidRPr="********">

<w:rPr>

<w:rtl w:val="0"/>

</w:rPr>

</w:r>

</w:p>

<w:sectPr>

<w:pgSz w:h="15840" w:w="12240" w:orient="portrait"/>

<w:pgMar w:bottom="1440" w:top="1440" w:left="1440" w:right="1440" w:header="720" w:footer="720"/>

<w:pgNumType w:start="1"/>

</w:sectPr>

</w:body>

</w:document>

