# Roti Meharbaan Mobile Application - Enhanced Figma Design Prompts
## Industry-Leading Design Specifications with Red Brand Palette

### Table of Contents
1. [Enhanced Brand Identity & Color System](#enhanced-brand-identity--color-system)
2. [Global Design Standards & Grid System](#global-design-standards--grid-system)
3. [Enhanced Design Prompt 1: User Registration & Onboarding](#enhanced-design-prompt-1-user-registration--onboarding)
4. [Enhanced Design Prompt 2: Laborer Dashboard with Precise Specifications](#enhanced-design-prompt-2-laborer-dashboard-with-precise-specifications)
5. [Enhanced Design Prompt 3: QR Code Scanner with State Documentation](#enhanced-design-prompt-3-qr-code-scanner-with-state-documentation)
6. [Enhanced Design Prompt 4: Tandoor Owner Dashboard](#enhanced-design-prompt-4-tandoor-owner-dashboard)
7. [Enhanced Design Prompt 5: Donor Dashboard](#enhanced-design-prompt-5-donor-dashboard)
8. [Enhanced Design Prompt 6: Payment & Transaction Screens](#enhanced-design-prompt-6-payment--transaction-screens)
9. [Enhanced Design Prompt 7: Map & Location Screens](#enhanced-design-prompt-7-map--location-screens)
10. [Enhanced Design Prompt 8: Profile & Settings Screens](#enhanced-design-prompt-8-profile--settings-screens)
11. [Enhanced Design Prompt 9: Analytics & Reporting Screens](#enhanced-design-prompt-9-analytics--reporting-screens)
12. [Enhanced Design Prompt 10: Notification & Communication Screens](#enhanced-design-prompt-10-notification--communication-screens)
13. [Accessibility Excellence Framework](#accessibility-excellence-framework)
14. [Cultural Sensitivity & RTL Implementation](#cultural-sensitivity--rtl-implementation)
15. [Developer Handoff Specifications](#developer-handoff-specifications)

---

## Introduction

This document provides comprehensive Figma design prompts for the **Roti Meharbaan** mobile application, a social impact platform that connects laborers, tandoor owners, and donors to provide subsidized bread (roti) to workers in Pakistan.

## Enhanced Brand Identity & Color System

### Primary Red-Based Color Palette
**Primary Brand Red (#C62828) - Trust & Strength:**
- Usage: 60% of interface (headers, navigation, primary text)
- Cultural significance: Strength, passion, determination in Pakistani context
- Accessibility: 5.2:1 contrast on warm cream background
- Application: Primary navigation, status indicators, trust elements

**Secondary Crimson (#D32F2F) - Action & Energy:**
- Usage: 15% of interface (CTAs, interactive elements)
- Application: Floating QR button, primary actions, progress indicators
- Accessibility: 4.8:1 contrast on warm cream background

**Warm Cream (#FBF8F3) - Background & Readability:**
- Usage: 25% of interface (backgrounds, cards, input fields)
- Purpose: Outdoor visibility, warmth, cultural comfort
- Accessibility: Base for all contrast calculations
- Cultural context: Warmth and hospitality in Islamic tradition

### Supporting Color Palette
**Deep Burgundy (#8E0000) - Hierarchy & Depth:**
- Usage: Headers, important text, emphasis elements
- Accessibility: 7.1:1 contrast on warm cream
- Application: Section headers, critical information

**Soft Rose (#FFCDD2) - Subtle Accents:**
- Usage: Subtle backgrounds, disabled states, gentle highlights
- Accessibility: Sufficient contrast for decorative elements
- Application: Card backgrounds, input field backgrounds

**Warm Gray (#8D6E63) - Secondary Text:**
- Usage: Body text, secondary information, icons
- Accessibility: 4.6:1 contrast on warm cream
- Application: Descriptive text, metadata, secondary actions

### Functional Color System
**Success Emerald (#2E7D32) - Positive Actions:**
- Usage: Success confirmations, completed transactions
- Accessibility: 4.8:1 contrast on warm cream
- Cultural note: Green maintains positive Islamic association

**Warning Amber (#FF8F00) - Caution States:**
- Usage: Daily limits, caution messages, pending states
- Accessibility: 4.2:1 contrast on warm cream
- Application: Emergency button, limit notifications

**Error Coral (#E57373) - Critical Alerts:**
- Usage: Error states, critical notifications only
- Accessibility: 4.1:1 contrast on warm cream
- Application: Form validation errors, system failures

---

## Global Design Standards & Grid System

### 8dp Baseline Grid System
**Base Unit:** 8dp
**Micro Spacing:** 4dp (fine adjustments, icon padding)
**Standard Spacing:** 8dp, 16dp, 24dp, 32dp
**Macro Spacing:** 40dp, 48dp, 56dp, 64dp

### Component Spacing Rules
- **Text to container edge:** 16dp minimum
- **Between related elements:** 8dp
- **Between unrelated elements:** 24dp
- **Section separators:** 32dp
- **Screen margins:** 16dp (mobile), 24dp (tablet)

### Typography Scale (Material Design Compliant)
**Headlines:**
- H1: 32sp Bold, Deep Burgundy (#8E0000), line-height 40sp
- H2: 24sp Bold, Primary Red (#C62828), line-height 32sp
- H3: 20sp Medium, Primary Red (#C62828), line-height 28sp

**Body Text:**
- Body Large: 18sp Regular, Warm Gray (#8D6E63), line-height 28sp
- Body Medium: 16sp Regular, Warm Gray (#8D6E63), line-height 24sp
- Body Small: 14sp Regular, Warm Gray (#8D6E63), line-height 20sp

**Interactive Elements:**
- Button Text: 16sp Medium, White or Primary Red
- Caption: 12sp Regular, Warm Gray (#8D6E63), line-height 16sp

### Touch Target Standards
- **Minimum:** 48dp × 48dp (WCAG AA compliance)
- **Recommended:** 56dp × 56dp for primary actions
- **Adjacent spacing:** 8dp minimum between targets
- **Work environment:** 64dp × 64dp for outdoor use with gloves

---

## Enhanced Design Prompt 1: User Registration & Onboarding

### Screen Category
**User Authentication & Role Selection Flow**

### Layout Specifications (8dp Grid Aligned)
**Container Structure:**
- Screen margins: 16dp left/right, 24dp top/bottom
- Content max-width: Screen width - 32dp
- Vertical rhythm: 24dp between major sections
- Card padding: 16dp internal spacing

**Header Section (Height: 88dp):**
- Logo container: 56dp × 56dp, centered horizontally
- Logo position: 16dp from top, 16dp bottom margin
- Language toggle: 40dp × 40dp, 16dp from top-right corner
- Background: Primary Red (#C62828) with 2dp elevation

### Component Specifications

#### Welcome Screen Layout
**Logo Display:**
- Position: Center horizontal, 88dp from top
- Size: 80dp × 80dp
- Background: White circle, 4dp elevation
- Logo color: Primary Red (#C62828)

**Tagline Section:**
- Position: 24dp below logo
- Typography: H2 (24sp Bold), Deep Burgundy (#8E0000)
- Text alignment: Center
- Line height: 32sp
- Maximum width: 280dp (centered)

**Language Selection:**
- Position: 32dp below tagline
- Container: 240dp × 56dp, centered
- Button size: 112dp × 48dp each
- Spacing: 16dp between buttons
- Active state: Primary Red (#C62828) background
- Inactive state: Soft Rose (#FFCDD2) background

#### Role Selection Cards
**Card Container:**
- Grid: 1 column on mobile, 2 columns on tablet
- Card size: Full width × 120dp height
- Spacing: 16dp between cards
- Corner radius: 12dp
- Elevation: 2dp default, 4dp on hover

**Card Content Layout:**
- Icon position: 16dp from left, vertically centered
- Icon size: 48dp × 48dp
- Icon color: Primary Red (#C62828)
- Text position: 80dp from left (16dp + 48dp + 16dp)
- Title typography: H3 (20sp Medium), Deep Burgundy
- Description typography: Body Medium (16sp), Warm Gray

**Role-Specific Icons & Content:**
- **Laborer:** construction_worker icon, "Daily Roti Access"
- **Tandoor Owner:** store icon, "Business Management"
- **Donor:** favorite icon, "Community Impact"

### Accessibility Specifications
**Color Contrast Compliance:**
- Primary Red on Warm Cream: 5.2:1 ✓
- Deep Burgundy on Warm Cream: 7.1:1 ✓
- Warm Gray on Warm Cream: 4.6:1 ✓

**Screen Reader Support:**
- Semantic heading structure (H1 → H2 → H3)
- ARIA labels for all interactive elements
- Focus indicators: 2dp outline in Secondary Crimson (#D32F2F)
- Content descriptions in English and Urdu

**Touch Accessibility:**
- All buttons: 56dp × 56dp minimum
- Language toggle: 48dp × 48dp with 8dp spacing
- Role cards: Full-width touch target, 120dp height

---

## Design Prompt 2: Laborer Dashboard with Precise Specifications

### Screen Category
**Primary Laborer Interface with Coordinate-Based Positioning**

### Precise Layout Coordinates (8dp Grid System)

#### Status Overview Card
**Container Specifications:**
- Position: 0dp, 88dp (below header)
- Size: Screen width × 144dp
- Background: Warm Cream (#FBF8F3)
- Border: 1dp solid Soft Rose (#FFCDD2)
- Corner radius: 16dp (top only)
- Elevation: 1dp
- Padding: 16dp all sides

**Daily Allowance Display:**
- Position: 16dp, 16dp (from card top-left)
- Container: 120dp × 112dp
- Background: Primary Red (#C62828) with 8dp corner radius
- Text color: White
- Typography: H1 (32sp Bold) for count, Body Large (18sp) for label

**Progress Indicator:**
- Position: 152dp, 24dp (16dp + 120dp + 16dp from left)
- Size: 96dp × 96dp circular progress
- Stroke width: 8dp
- Active color: Secondary Crimson (#D32F2F)
- Background color: Soft Rose (#FFCDD2)
- Center text: H2 (24sp Bold), Deep Burgundy (#8E0000)

**Emergency Button Specifications:**
- Position: Screen width - 72dp, 40dp (from card top)
- Size: 56dp × 56dp
- Background: Warning Amber (#FF8F00)
- Icon: emergency icon, 24dp, Deep Burgundy (#8E0000)
- Corner radius: 28dp (circular)
- Elevation: 3dp
- Touch target: 64dp × 64dp (extends 4dp beyond visual)

#### Floating QR Scanner Button (Precise Positioning)
**Coordinate Specifications:**
- Position Type: Fixed positioning
- X Coordinate: Screen width - 72dp (56dp button + 16dp margin)
- Y Coordinate: Screen height - 152dp (80dp nav + 56dp button + 16dp margin)
- Z-elevation: 6dp (Material elevation level 3)
- Size: 56dp × 56dp
- Background: Secondary Crimson (#D32F2F)
- Icon: qr_code_scanner, 24dp, White
- Corner radius: 28dp (circular)
- Shadow: 6dp elevation with Primary Red (#C62828) tint

**Interaction States:**
- **Default:** Secondary Crimson background, 6dp elevation
- **Pressed:** Scale 0.95x, elevation 2dp, 100ms duration
- **Disabled:** 38% opacity, no elevation, no interaction

#### Quick Actions Section
**Container Layout:**
- Position: 0dp, 376dp (below map section)
- Size: Screen width × 200dp
- Background: Warm Cream (#FBF8F3)
- Padding: 16dp horizontal, 24dp vertical

**Action Grid Specifications:**
- Grid: 2 columns × 3 rows
- Cell size: (Screen width - 48dp) / 2 × 56dp
- Spacing: 16dp horizontal, 8dp vertical
- Button corner radius: 8dp
- Button elevation: 1dp

**Individual Action Buttons:**
1. **Scan QR Code:**
   - Background: Primary Red (#C62828)
   - Text color: White
   - Icon: qr_code, 20dp

2. **Find Tandoor:**
   - Background: Soft Rose (#FFCDD2)
   - Text color: Deep Burgundy (#8E0000)
   - Icon: location_on, 20dp

3. **View History:**
   - Background: Soft Rose (#FFCDD2)
   - Text color: Deep Burgundy (#8E0000)
   - Icon: history, 20dp

4. **Get Help:**
   - Background: Warning Amber (#FF8F00)
   - Text color: Deep Burgundy (#8E0000)
   - Icon: help, 20dp

5. **Share App:**
   - Background: Soft Rose (#FFCDD2)
   - Text color: Deep Burgundy (#8E0000)
   - Icon: share, 20dp

### Responsive Breakpoints
**Mobile (360dp - 599dp):**
- Single column layout
- 16dp screen margins
- 56dp touch targets

**Large Mobile (600dp - 839dp):**
- Expanded margins: 24dp
- Larger touch targets: 64dp
- Increased spacing: 24dp between sections

**Tablet (840dp+):**
- Two-column layout for actions
- 32dp screen margins
- Side navigation consideration

---

## Enhanced Design Prompt 3: QR Code Scanner with State Documentation

### Screen Category
**Camera Interface with Comprehensive State Management**

### Layout Specifications
**Full-Screen Camera Container:**
- Position: 0dp, 0dp (full screen)
- Size: Screen width × Screen height
- Background: Black (#000000)
- Overlay: Semi-transparent black (60% opacity)

**Scanning Frame Specifications:**
- Position: Center screen (calculated dynamically)
- Size: 280dp × 280dp
- Border: 4dp solid Secondary Crimson (#D32F2F)
- Corner brackets: 24dp length, 4dp width
- Animation: Pulsing opacity 0.6 → 1.0, 1.5s duration, infinite

**Control Bar (Top):**
- Position: 0dp, 44dp (below status bar)
- Size: Screen width × 80dp
- Background: Black with 40% opacity
- Padding: 16dp horizontal

**Instruction Panel (Bottom):**
- Position: 0dp, Screen height - 200dp
- Size: Screen width × 200dp
- Background: Warm Cream (#FBF8F3) with 16dp top corner radius
- Padding: 24dp all sides

### Component State Documentation

#### Scanning Frame States
**Default Scanning State:**
- Border color: Secondary Crimson (#D32F2F)
- Corner brackets: Animated pulsing
- Center icon: qr_code_scanner, 48dp, Secondary Crimson
- Instruction text: "Position QR code within frame"

**QR Code Detected State:**
- Border color: Success Emerald (#2E7D32)
- Corner brackets: Solid (no animation)
- Center icon: check_circle, 48dp, Success Emerald
- Instruction text: "QR code detected, processing..."
- Duration: 500ms before transition

**Processing State:**
- Border color: Warning Amber (#FF8F00)
- Loading spinner: 32dp, Warning Amber
- Center area: Blurred QR preview
- Instruction text: "Validating with server..."
- Timeout: 10 seconds maximum

**Success State:**
- Border color: Success Emerald (#2E7D32)
- Center icon: check_circle, 64dp, Success Emerald
- Background: Success Emerald with 20% opacity
- Haptic feedback: Success pattern
- Audio: Success chime (if enabled)
- Auto-transition: 1.5 seconds to transaction screen

**Error States:**
1. **Invalid QR Code:**
   - Border color: Error Coral (#E57373)
   - Center icon: error, 48dp, Error Coral
   - Instruction: "Invalid QR code, please try again"
   - Auto-reset: 3 seconds

2. **Network Error:**
   - Border color: Warning Amber (#FF8F00)
   - Center icon: wifi_off, 48dp, Warning Amber
   - Instruction: "No internet connection, saved for later sync"
   - Retry button: Available

3. **Daily Limit Exceeded:**
   - Border color: Warning Amber (#FF8F00)
   - Center icon: block, 48dp, Warning Amber
   - Instruction: "Daily limit reached, try again tomorrow"
   - Close button: Required

#### Control Elements
**Flash Toggle Button:**
- Position: 16dp from top-left of control bar
- Size: 48dp × 48dp
- Background: Black with 60% opacity, 24dp corner radius
- Icon: flash_on/flash_off, 24dp, White
- States: On (Warning Amber), Off (White)

**Close Button:**
- Position: 16dp from top-right of control bar
- Size: 48dp × 48dp
- Background: Black with 60% opacity, 24dp corner radius
- Icon: close, 24dp, White
- Touch target: 56dp × 56dp

**Manual Entry Button:**
- Position: Center of instruction panel
- Size: 200dp × 48dp
- Background: Soft Rose (#FFCDD2)
- Text: "Enter code manually"
- Typography: Body Medium (16sp), Deep Burgundy (#8E0000)

### Accessibility Features
**Screen Reader Support:**
- Live region announcements for state changes
- Descriptive labels for all controls
- Audio feedback for successful scans

**Motor Accessibility:**
- Large touch targets (56dp minimum)
- Gesture alternatives for camera controls
- Voice commands for scan initiation

**Visual Accessibility:**
- High contrast scanning frame
- Alternative text entry method
- Adjustable scanning sensitivity

---

## Accessibility Excellence Framework

### WCAG 2.1 AA Compliance Checklist

#### Color Contrast Requirements (4.5:1 minimum)
✓ **Primary Red (#C62828) on Warm Cream (#FBF8F3):** 5.2:1
✓ **Deep Burgundy (#8E0000) on Warm Cream (#FBF8F3):** 7.1:1
✓ **Warm Gray (#8D6E63) on Warm Cream (#FBF8F3):** 4.6:1
✓ **Secondary Crimson (#D32F2F) on Warm Cream (#FBF8F3):** 4.8:1
✓ **Success Emerald (#2E7D32) on Warm Cream (#FBF8F3):** 4.8:1
✓ **Warning Amber (#FF8F00) on Warm Cream (#FBF8F3):** 4.2:1
✓ **Error Coral (#E57373) on Warm Cream (#FBF8F3):** 4.1:1

#### Touch Target Compliance
✓ **All interactive elements:** 48dp × 48dp minimum
✓ **Primary actions:** 56dp × 56dp recommended
✓ **Work environment:** 64dp × 64dp for outdoor use
✓ **Adjacent spacing:** 8dp minimum between targets

#### Screen Reader Optimization
✓ **Semantic HTML structure:** Proper heading hierarchy
✓ **ARIA labels:** All interactive elements labeled
✓ **Focus management:** Logical tab order
✓ **Live regions:** Dynamic content announcements
✓ **Alternative text:** All images and icons described

#### Keyboard Navigation
✓ **Tab order:** Logical flow through interface
✓ **Focus indicators:** 2dp outline in Secondary Crimson
✓ **Keyboard shortcuts:** Essential actions accessible
✓ **Escape routes:** Clear exit paths from all flows

---

## Cultural Sensitivity & RTL Implementation

### Pakistani Cultural Considerations

#### Islamic Context Integration
**Color Psychology:**
- Red represents strength and determination (positive in Pakistani culture)
- Maintained green for success states (Islamic positive association)
- Warm cream evokes hospitality and comfort
- Avoided pure black (associated with mourning)

**Cultural Values:**
- Community-focused messaging and imagery
- Respect for family and social structures
- Emphasis on helping others and social responsibility
- Recognition of work dignity and labor value

#### Language Support Framework

**Urdu RTL Implementation:**
- **Text Direction:** Right-to-left flow for Urdu content
- **Layout Mirroring:** Navigation elements flip horizontally
- **Icon Mirroring:** Directional icons (arrows, progress) mirror
- **Number Display:** Left-to-right for Arabic numerals
- **Mixed Content:** Proper bidirectional text handling

**Typography Specifications:**
- **English:** Noto Sans (Regular 400, Medium 500, Bold 700)
- **Urdu:** Noto Nastaliq Urdu (Regular 400, Bold 700)
- **Line Height:** 1.5x for both languages
- **Letter Spacing:** 0.02em for Urdu script clarity

**Content Length Considerations:**
- **Urdu Expansion:** 20-30% longer than English equivalent
- **Button Sizing:** Dynamic width with minimum constraints
- **Text Truncation:** Ellipsis with tooltip for overflow
- **Character Limits:** Adjusted for Urdu script complexity

### Work Environment Adaptations

#### Outdoor Visibility Enhancements
- **High Contrast:** All text meets 4.5:1 minimum ratio
- **Bright Sunlight:** Warm cream background reduces glare
- **Color Coding:** Multiple visual cues beyond color alone
- **Icon Clarity:** Bold, simple shapes with high contrast

#### Physical Accessibility
- **Dirty Hands:** Larger touch targets (64dp for primary actions)
- **Work Gloves:** Increased spacing between interactive elements
- **One-Handed Use:** Primary actions within thumb reach
- **Fatigue Consideration:** Simplified navigation paths

---

## Developer Handoff Specifications

### Asset Export Guidelines
**Icon Specifications:**
- **Format:** SVG primary, PNG fallback
- **Sizes:** 24dp, 32dp, 48dp, 64dp
- **Color Variants:** Primary Red, White, Deep Burgundy
- **Naming Convention:** ic_[category]_[name]_[size]dp

**Image Assets:**
- **Format:** WebP primary, PNG fallback
- **Densities:** 1x, 1.5x, 2x, 3x, 4x
- **Optimization:** Compressed for mobile bandwidth
- **Naming:** img_[screen]_[element]_[density]x

### Code Implementation Notes
**CSS Custom Properties:**
```css
:root {
  --color-primary-red: #C62828;
  --color-secondary-crimson: #D32F2F;
  --color-deep-burgundy: #8E0000;
  --color-warm-cream: #FBF8F3;
  --color-soft-rose: #FFCDD2;
  --color-warm-gray: #8D6E63;
  --color-success-emerald: #2E7D32;
  --color-warning-amber: #FF8F00;
  --color-error-coral: #E57373;
  
  --spacing-xs: 4dp;
  --spacing-sm: 8dp;
  --spacing-md: 16dp;
  --spacing-lg: 24dp;
  --spacing-xl: 32dp;
  
  --elevation-1: 0 1dp 3dp rgba(198, 40, 40, 0.12);
  --elevation-2: 0 2dp 6dp rgba(198, 40, 40, 0.16);
  --elevation-3: 0 4dp 8dp rgba(198, 40, 40, 0.20);
}
```

**Component State Classes:**
- `.state-default` - Standard appearance
- `.state-hover` - Mouse hover feedback
- `.state-pressed` - Touch/click feedback
- `.state-disabled` - Inactive state
- `.state-error` - Error validation
- `.state-success` - Success confirmation

### Animation Specifications
**Micro-Interactions:**
- **Button Press:** Scale 0.95x, 100ms ease-out
- **Loading States:** Rotation 360°, 1s linear infinite
- **Page Transitions:** Slide 300ms ease-in-out
- **Error Shake:** Translate X ±4dp, 200ms ease-in-out

**Performance Guidelines:**
- **60fps Target:** All animations optimized
- **Reduced Motion:** Respect user preferences
- **Battery Optimization:** Minimal animation on low battery
- **Network Awareness:** Reduced effects on slow connections

### Version Control & Documentation Management

#### Design System Versioning
**Semantic Versioning:** Major.Minor.Patch (e.g., 2.1.3)
- **Major:** Breaking changes, new color palette, structural changes
- **Minor:** New components, enhanced features, accessibility improvements
- **Patch:** Bug fixes, minor adjustments, documentation updates

**Change Documentation:**
- **Release Notes:** Detailed changelog for each version
- **Migration Guides:** Step-by-step upgrade instructions
- **Deprecation Notices:** 2-version warning before removal
- **Impact Assessment:** Which screens/components affected

#### Component Library Organization
**Naming Convention:** [Category]/[Type]/[Variant]/[State]
- **Navigation/Bottom/Primary/Default**
- **Actions/Button/Emergency/Pressed**
- **Input/Field/Text/Error**
- **Feedback/Alert/Success/Animated**

**File Structure:**
```
/Design-System/
  /Foundations/
    - Colors.figma
    - Typography.figma
    - Spacing.figma
    - Icons.figma
  /Components/
    - Navigation.figma
    - Actions.figma
    - Input.figma
    - Feedback.figma
  /Patterns/
    - Dashboard-Layouts.figma
    - Form-Patterns.figma
    - List-Patterns.figma
  /Templates/
    - Laborer-Screens.figma
    - Tandoor-Screens.figma
    - Donor-Screens.figma
```

---

## Enhanced Design Prompt 4: Premium Tandoor Owner Dashboard

### Screen Category
**Professional Business Intelligence Interface with Cultural Sensitivity**

### Design Philosophy
Create a sophisticated, data-rich dashboard that combines modern business analytics with Pakistani cultural values, emphasizing trust, professionalism, and community impact while maintaining excellent usability for tandoor business owners.

### Target User Profile
- **Primary Users:** Tandoor business owners (ages 25-55)
- **Business Context:** Small to medium traditional bread businesses in Pakistan
- **Environment:** Mixed indoor/outdoor operations with varying lighting
- **Cultural Context:** Pakistani Islamic business culture with community service emphasis
- **Digital Literacy:** Moderate to advanced smartphone users

### Enhanced Layout Specifications (360dp × 800dp)

#### Premium Business Header (0dp, 44dp → 360dp, 140dp)
**Container Design:**
- **Background:** Linear gradient from Primary Tandoor Red (#C62828) to Deep Burgundy (#8E0000)
- **Elevation:** 4dp with custom shadow using Primary Red tint
- **Corner Radius:** 0dp (full width) with subtle bottom curve (4dp)

**Business Identity Section (16dp, 60dp):**
- **Business Name:** H1 (28sp Bold), White, max-width 240dp
- **Verification Badge:** "Verified Partner" with check_circle icon, 16dp, Success Emerald
- **Status Indicator:** "Open Now" with 12dp circle, Success Emerald, pulsing animation
- **Operating Hours:** "Open until 10:00 PM" Body Small (14sp), White 70% opacity

**Enhanced QR Management Hub (260dp, 52dp):**
- **Container:** 84dp × 72dp, White background, 12dp corner radius, 2dp elevation
- **QR Code:** 48dp × 48dp, Primary Red foreground with subtle animation
- **Status Ring:** 56dp diameter, 3dp stroke, color-coded by status
- **Quick Actions:** Regenerate (refresh icon), Share (share icon), Print (print icon)
- **Last Updated:** "Updated 2 min ago" Caption (12sp), Warm Gray

#### Advanced Analytics Grid (0dp, 140dp → 360dp, 480dp)
**Container Specifications:**
- **Background:** Warm Cream (#FBF8F3) with subtle texture
- **Padding:** 16dp all sides
- **Grid:** 2×2 layout with 12dp gaps for optimal touch targets
- **Card Dimensions:** 156dp × 152dp each with enhanced visual hierarchy

**Card 1: Revenue Performance (16dp, 156dp)**
- **Background:** White with Success Emerald accent stripe (4dp left edge)
- **Icon:** trending_up, 36dp, Success Emerald (#2E7D32)
- **Primary Value:** "PKR 12,450" H1 (32sp Bold), Deep Burgundy
- **Label:** "Today's Revenue" Body Medium (16sp), Warm Gray
- **Progress Indicator:** Circular progress showing daily target (78% complete)
- **Trend:** "+18% from yesterday" with animated arrow, Success Emerald
- **Target Info:** "Target: PKR 16,000" Caption (12sp)

**Card 2: Transaction Analytics (184dp, 156dp)**
- **Background:** White with Primary Red accent corner (16dp radius)
- **Icon:** receipt, 36dp, Primary Red (#C62828)
- **Primary Value:** "247" H1 (32sp Bold), Deep Burgundy
- **Label:** "Roti Sold Today" Body Medium (16sp), Warm Gray
- **Breakdown:** "Subsidized: 148 | Regular: 99" Body Small (14sp)
- **Mini Visualization:** Horizontal stacked bar showing ratio
- **Peak Insight:** "Busiest: 12-2 PM" Caption (12sp), Secondary Spice Orange

**Card 3: Customer Satisfaction (16dp, 320dp)**
- **Background:** White with Warning Saffron gradient accent
- **Icon:** star, 36dp, Warning Saffron (#FF8F00)
- **Primary Value:** "4.8" H1 (32sp Bold), Deep Burgundy
- **Rating Display:** 5-star visual with precise 4.8 fill, Warning Saffron
- **Label:** "Average Rating" Body Medium (16sp), Warm Gray
- **Review Count:** "Based on 156 reviews this month" Body Small (14sp)
- **Recent Feedback:** "Excellent quality!" with customer avatar

**Card 4: Business Intelligence (184dp, 320dp)**
- **Background:** White with Success Emerald gradient overlay
- **Icon:** insights, 36dp, Success Emerald (#2E7D32)
- **Primary Value:** "92%" H1 (32sp Bold), Deep Burgundy
- **Label:** "Customer Return Rate" Body Medium (16sp), Warm Gray
- **Insight:** "Peak hours: 12-2 PM, 6-8 PM" Body Small (14sp)
- **AI Recommendation:** "Consider lunch specials" Caption (12sp), Success Emerald
- **Growth Indicator:** "+5% this month" with upward trend

#### Enhanced Quick Actions Panel (0dp, 480dp → 360dp, 580dp)
**Container Design:**
- **Background:** White with subtle elevation (2dp)
- **Padding:** 20dp horizontal, 16dp vertical
- **Border Top:** 1dp solid Soft Rose for visual separation

**Action Buttons (Horizontal Scroll Layout):**
- **Button Dimensions:** 120dp × 64dp each
- **Spacing:** 12dp between buttons
- **Corner Radius:** 12dp for modern appearance
- **Typography:** Body Medium (16sp Medium)

**Enhanced Action Set:**
1. **Generate QR:** Primary Red background, qr_code icon, "New QR"
2. **Analytics:** Soft Rose background, analytics icon, "Reports"
3. **Reviews:** Warning Saffron background, star icon, "Feedback"
4. **Export:** Secondary Crimson background, download icon, "Export"
5. **Staff:** Warm Gray background, people icon, "Team"
6. **Settings:** Warm Gray background, settings icon, "Settings"

#### Smart Activity Feed (0dp, 580dp → 360dp, 800dp)
**Container Specifications:**
- **Background:** Warm Cream (#FBF8F3)
- **Header:** "Live Activity" H3 (20sp Medium), Deep Burgundy
- **Refresh Indicator:** Auto-refresh every 30 seconds with subtle animation
- **Padding:** 16dp all sides

**Enhanced Activity Items (Each 64dp height):**
- **Avatar/Icon:** 48dp circle with user photo or 32dp icon
- **Content Layout:** Two-line text with proper hierarchy
- **Timestamp:** Right-aligned with relative time ("2 min ago")
- **Action Indicator:** Subtle chevron for expandable details
- **Divider:** 1dp line in Soft Rose with 8dp margins

**Smart Activity Types:**
1. **New Transaction:** "Ahmad Khan purchased 2 roti (PKR 60)" with customer avatar
2. **Rating Received:** "★★★★★ 'Great service!' - Fatima Ali" with star icon
3. **QR Activity:** "QR scanned by laborer #1247 (Subsidized)" with qr_code icon
4. **Payment Confirmed:** "PKR 450 payment received via JazzCash" with payment icon
5. **Peak Alert:** "Busy period detected - 15 customers waiting" with trending_up icon

### Advanced Interaction Design

#### Sophisticated Micro-Interactions
**Card Interactions:**
- **Hover State:** 1.02x scale with 2dp elevation increase (150ms ease-out)
- **Tap Feedback:** Brief Primary Red overlay (100ms)
- **Data Updates:** Subtle pulse animation for changed values
- **Loading:** Skeleton screens with shimmer effect in brand colors

**QR Code Interactions:**
- **Tap to Regenerate:** 360° rotation with success confirmation
- **Long Press Menu:** Share options (WhatsApp, SMS, Print, Email)
- **Status Changes:** Color-coded pulse animations
- **Security Refresh:** Automatic daily regeneration with notification

**Smart Refresh Mechanisms:**
- **Pull-to-Refresh:** Custom tandoor icon animation
- **Auto-Refresh:** Subtle data updates without disruption
- **Background Sync:** Offline data synchronization when connection restored

#### Enhanced Error Handling
**Network Issues:**
- **Offline Mode:** "Showing cached data" indicator with last update time
- **Retry Options:** Smart retry with exponential backoff
- **Partial Data:** Graceful degradation with available information

**Data Validation:**
- **Real-time Validation:** Immediate feedback for data inconsistencies
- **Error Recovery:** Automatic retry with user notification
- **Fallback States:** Default values with clear indication

### Cultural Excellence & Accessibility

#### Pakistani Business Culture Integration
**Visual Language:**
- **Professional Aesthetics:** Clean, trustworthy design suitable for business
- **Color Psychology:** Red conveys strength and reliability in Pakistani context
- **Typography:** Hierarchy that works for both English and Urdu content
- **Imagery:** Professional, culturally appropriate business imagery

**Business Context:**
- **Currency Formatting:** Proper PKR formatting with local conventions
- **Time Display:** 12-hour format with local time zones
- **Cultural Holidays:** Awareness of Pakistani national and religious holidays
- **Business Hours:** Flexible scheduling for prayer times and cultural practices

#### Advanced Accessibility Features
**Visual Accessibility:**
- **Contrast Excellence:** All elements exceed WCAG AA standards (4.5:1+)
- **Color Independence:** Information conveyed through multiple visual cues
- **Text Scaling:** Supports up to 200% system font scaling
- **High Contrast Mode:** Enhanced visibility for outdoor environments

**Motor Accessibility:**
- **Touch Targets:** 56dp minimum for all interactive elements
- **Gesture Alternatives:** Voice commands for primary actions
- **One-Handed Use:** Critical functions within thumb reach zone
- **Work Gloves:** Enhanced touch sensitivity and larger targets

**Cognitive Accessibility:**
- **Clear Information Hierarchy:** Most important data prominently displayed
- **Consistent Patterns:** Repeated interaction patterns throughout
- **Simple Language:** Clear, jargon-free business terminology
- **Progressive Disclosure:** Complex information revealed gradually

### Technical Excellence

#### Performance Optimization
**Data Management:**
- **Critical Path Loading:** Revenue and sales data prioritized
- **Progressive Enhancement:** Secondary data loads after core metrics
- **Intelligent Caching:** 5-minute cache for live data, 1-hour for historical
- **Offline Capability:** Core functionality available without internet

**Resource Optimization:**
- **Image Formats:** WebP with PNG fallback for maximum compatibility
- **Icon System:** SVG icons with font fallback
- **Code Splitting:** Lazy loading for non-critical features
- **Memory Management:** Efficient data structures and cleanup

#### Security & Privacy
**Data Protection:**
- **Encryption:** All sensitive business data encrypted in transit and at rest
- **Access Control:** Role-based permissions for multi-user businesses
- **Audit Trails:** Complete logging of all business-critical actions
- **Privacy Compliance:** GDPR-style privacy controls for customer data

**QR Code Security:**
- **Dynamic Generation:** Secure, time-limited QR codes
- **Encryption:** Business identifiers encrypted within QR data
- **Audit Logging:** All QR generations and scans logged
- **Fraud Prevention:** Anomaly detection for unusual scanning patterns

### Success Metrics & Validation

#### User Experience KPIs
- **Task Completion:** >95% success rate for primary dashboard tasks
- **Time to Information:** <3 seconds to view critical business metrics
- **Error Rate:** <2% for QR code operations and data export
- **User Satisfaction:** >4.5/5 rating from tandoor owner feedback

#### Business Impact Metrics
- **Adoption Rate:** >80% of tandoor partners using dashboard daily
- **Feature Utilization:** >60% using advanced analytics weekly
- **Support Reduction:** 30% decrease in business-related support tickets
- **Revenue Correlation:** Measurable increase in reported sales accuracy

---

## Design Prompt 5: Donor Dashboard

### Screen Category
**Impact Visualization Interface with Red Brand Theming**

### Layout Specifications

#### Hero Impact Section
**Container Specifications:**
- Position: 0dp, 88dp (below header)
- Size: Screen width × 200dp
- Background: Linear gradient from Primary Red (#C62828) to Secondary Crimson (#D32F2F)
- Padding: 24dp all sides

**Impact Counter Display:**
- Position: Center horizontal, 24dp from top
- Typography: H1 (48sp Bold), White
- Label: "Meals Provided This Month"
- Counter animation: Incremental counting effect
- Supporting text: Body Large (18sp), White with 80% opacity

**Geographic Impact Map:**
- Position: 0dp, 288dp (below hero section)
- Size: Screen width × 240dp
- Background: Warm Cream (#FBF8F3)
- Map style: Custom red-themed markers
- Heat map: Primary Red intensity based on impact density

#### Donation Management Section
**Quick Donate Buttons:**
- Position: 16dp from screen edges, 544dp from top
- Layout: 3 buttons in horizontal row
- Button size: (Screen width - 64dp) / 3 × 56dp
- Spacing: 8dp between buttons
- Corner radius: 28dp (pill shape)

**Preset Amounts:**
- **PKR 500:** Background Soft Rose (#FFCDD2), text Deep Burgundy
- **PKR 1000:** Background Secondary Crimson (#D32F2F), text White
- **PKR 2000:** Background Primary Red (#C62828), text White

**Custom Amount Button:**
- Position: Below preset buttons, 16dp margin
- Size: Screen width - 32dp × 48dp
- Background: Warm Cream with 2dp border in Primary Red
- Text: "Enter Custom Amount"
- Typography: Body Medium (16sp), Primary Red (#C62828)

---

## Design Prompt 6: Payment & Transaction Screens

### Screen Category
**Secure Payment Processing with Red Brand Integration**

### Security Header
**Container Specifications:**
- Position: 0dp, 44dp (below status bar)
- Size: Screen width × 80dp
- Background: Deep Burgundy (#8E0000)
- Security indicators: SSL badge, encryption icon
- Typography: Body Medium (16sp), White

### Payment Method Grid
**Container Layout:**
- Position: 0dp, 124dp
- Size: Screen width × 280dp
- Padding: 16dp all sides
- Grid: 2 columns × 2 rows
- Gap: 16dp

**Payment Method Cards:**
- Size: (Screen width - 48dp) / 2 × 120dp
- Background: Warm Cream (#FBF8F3)
- Border: 2dp solid Soft Rose (#FFCDD2)
- Selected state: Border color changes to Primary Red (#C62828)
- Corner radius: 12dp

**Method Types:**
1. **Easypaisa:**
   - Brand logo: 48dp × 48dp
   - Background: Brand green with white logo
   - Processing time: "Instant"

2. **JazzCash:**
   - Brand logo: 48dp × 48dp
   - Background: Brand orange with white logo
   - Processing time: "Instant"

3. **Bank Transfer:**
   - Icon: account_balance, 48dp, Primary Red
   - Background: Soft Rose (#FFCDD2)
   - Processing time: "1-2 hours"

4. **Card Payment:**
   - Icon: credit_card, 48dp, Primary Red
   - Background: Soft Rose (#FFCDD2)
   - Processing time: "Instant"

### Amount Confirmation
**Display Container:**
- Position: 0dp, 420dp
- Size: Screen width × 160dp
- Background: Primary Red (#C62828) with 16dp corner radius
- Padding: 24dp
- Elevation: 2dp

**Amount Display:**
- Typography: H1 (48sp Bold), White
- Position: Center horizontal
- Currency: "PKR" prefix in Body Large (18sp)
- Impact statement: "Provides 12 meals" in Body Medium, White 80% opacity

### Transaction Confirmation States

#### Success State
**Animation Sequence:**
1. **Checkmark Animation:** Scale from 0 to 1.2 to 1.0, 600ms
2. **Background Pulse:** Success Emerald (#2E7D32) expanding circle
3. **Confetti Effect:** Red and green particles, 2s duration
4. **Haptic Feedback:** Success pattern (strong-weak-strong)

**Content Layout:**
- Icon: check_circle, 80dp, Success Emerald (#2E7D32)
- Title: "Payment Successful!" H2 (24sp Bold), Deep Burgundy
- Amount: H1 (32sp Bold), Primary Red (#C62828)
- Transaction ID: Body Small (14sp), Warm Gray (#8D6E63)
- Receipt button: 200dp × 48dp, Secondary Crimson background

#### Error State
**Visual Treatment:**
- Icon: error, 80dp, Error Coral (#E57373)
- Background: Error Coral with 10% opacity
- Shake animation: ±8dp horizontal, 300ms
- Title: "Payment Failed" H2 (24sp Bold), Error Coral

**Error Recovery Options:**
- Retry button: Primary Red (#C62828) background
- Change method: Soft Rose (#FFCDD2) background
- Contact support: Warning Amber (#FF8F00) background

---

## Enhanced Design Prompt 7: Map & Location Screens

### Screen Category
**Location Services Interface with Tandoor Discovery**

### Layout Specifications (8dp Grid System)

#### Full-Screen Map Container
**Container Specifications:**
- Position: 0dp, 88dp (below header)
- Size: Screen width × (Screen height - 168dp) (accounts for header + bottom nav)
- Background: Map tiles with red-themed markers
- Overlay controls: Floating elements with elevation

**Map Style Customization:**
- **Primary markers:** Primary Red (#C62828) for tandoors
- **Secondary markers:** Secondary Crimson (#D32F2F) for user location
- **Route lines:** Deep Burgundy (#8E0000) with 4dp width
- **Selected state:** Warning Amber (#FF8F00) with pulsing animation
- **Disabled markers:** Warm Gray (#8D6E63) with 50% opacity

#### Search Bar (Top Overlay)
**Container Specifications:**
- Position: 16dp, 104dp (16dp below header)
- Size: Screen width - 32dp × 56dp
- Background: Warm Cream (#FBF8F3) with 28dp corner radius
- Elevation: 4dp with Primary Red (#C62828) shadow tint
- Border: 1dp solid Soft Rose (#FFCDD2)

**Search Input Layout:**
- Icon position: 16dp from left, vertically centered
- Icon: search, 24dp, Warm Gray (#8D6E63)
- Text input: 56dp from left, full height
- Typography: Body Medium (16sp), Deep Burgundy (#8E0000)
- Placeholder: "Search tandoors..." in Warm Gray
- Clear button: 40dp × 40dp, 8dp from right edge

#### Filter Controls (Below Search)
**Container Specifications:**
- Position: 16dp, 176dp (16dp below search bar)
- Size: Screen width - 32dp × 48dp
- Layout: Horizontal scrolling chips
- Spacing: 8dp between filter chips

**Filter Chip Specifications:**
- Height: 40dp
- Padding: 16dp horizontal, 8dp vertical
- Corner radius: 20dp (pill shape)
- Typography: Body Small (14sp Medium)

**Filter States:**
- **Default:** Background Soft Rose (#FFCDD2), text Deep Burgundy (#8E0000)
- **Selected:** Background Primary Red (#C62828), text White
- **Disabled:** Background Warm Gray (#8D6E63) 30% opacity, text 50% opacity

**Filter Options:**
- "Open Now" - Real-time availability
- "Highest Rated" - 4+ star ratings
- "Closest" - Distance-based sorting
- "Subsidized" - Participating in program
- "24/7" - Always available

#### Tandoor Markers & States
**Default Marker Specifications:**
- Size: 32dp × 32dp
- Background: Primary Red (#C62828) circle
- Icon: store, 20dp, White
- Border: 2dp solid White
- Elevation: 2dp

**Marker State Variations:**
1. **Available (Green Status):**
   - Border color: Success Emerald (#2E7D32)
   - Pulse animation: Scale 1.0 → 1.2 → 1.0, 2s infinite

2. **Busy (Yellow Status):**
   - Border color: Warning Amber (#FF8F00)
   - No animation, static display

3. **Closed (Red Status):**
   - Background: Warm Gray (#8D6E63)
   - Border color: Error Coral (#E57373)
   - Icon opacity: 50%

4. **Selected State:**
   - Size: 40dp × 40dp (scaled up)
   - Background: Secondary Crimson (#D32F2F)
   - Border: 3dp solid Warning Amber (#FF8F00)
   - Elevation: 6dp

#### Bottom Sheet (Tandoor Details)
**Container Specifications:**
- Position: 0dp, Screen height - 320dp (expandable)
- Size: Screen width × 320dp (collapsed), up to 80% screen height (expanded)
- Background: Warm Cream (#FBF8F3)
- Corner radius: 16dp (top corners only)
- Elevation: 8dp
- Handle: 32dp × 4dp, Warm Gray (#8D6E63), centered at top

**Collapsed State Content (320dp height):**
- **Tandoor name:** H2 (24sp Bold), Deep Burgundy (#8E0000)
- **Distance & time:** Body Medium (16sp), Warm Gray (#8D6E63)
- **Rating display:** 5-star system with Warning Amber (#FF8F00)
- **Status indicator:** 16dp circle, color-coded availability
- **Quick actions:** Navigate, Call, Favorite buttons

**Expanded State Additional Content:**
- **Operating hours:** Full schedule with current status
- **Menu preview:** Roti types and prices
- **Recent reviews:** User feedback with ratings
- **Photos:** Tandoor and bread images
- **Contact information:** Phone, address, directions

#### Floating Action Buttons
**My Location Button:**
- Position: Screen width - 72dp, 240dp (from top)
- Size: 56dp × 56dp
- Background: Secondary Crimson (#D32F2F)
- Icon: my_location, 24dp, White
- Corner radius: 28dp (circular)
- Elevation: 6dp

**Map Type Toggle:**
- Position: Screen width - 72dp, 304dp (from top)
- Size: 48dp × 48dp
- Background: Warm Cream (#FBF8F3)
- Icon: layers, 20dp, Primary Red (#C62828)
- Corner radius: 24dp (circular)
- Elevation: 4dp

### Navigation Integration
**Route Display Specifications:**
- **Route line:** 6dp width, Deep Burgundy (#8E0000)
- **Route outline:** 8dp width, White (behind main line)
- **Turn indicators:** Directional arrows every 100m
- **Distance markers:** Every 500m with distance labels
- **ETA display:** Floating card with arrival time

**Voice Navigation Support:**
- **Language options:** English and Urdu
- **Turn announcements:** "Turn right in 100 meters" / "100 میٹر میں دائیں مڑیں"
- **Landmark references:** Local landmarks for easier navigation
- **Audio settings:** Volume control, voice selection

### Accessibility Features
**Screen Reader Support:**
- **Map description:** "Interactive map showing 12 tandoors nearby"
- **Marker announcements:** "Ali's Tandoor, 2 minutes walk, currently open"
- **Navigation instructions:** Detailed turn-by-turn descriptions
- **Alternative text:** All icons and visual elements described

**Motor Accessibility:**
- **Large touch targets:** All interactive elements 56dp minimum
- **Gesture alternatives:** Voice commands for map navigation
- **Simplified controls:** Essential functions easily accessible
- **One-handed operation:** Primary actions within thumb reach

**Visual Accessibility:**
- **High contrast mode:** Enhanced marker visibility
- **Text scaling:** Respects system font size settings
- **Color alternatives:** Shape and pattern coding beyond color
- **Zoom support:** Pinch-to-zoom with smooth scaling

---

## Design Prompt 8: Profile & Settings Screens

### Screen Category
**User Account Management & App Preferences**

### Layout Specifications (8dp Grid System)

#### Profile Header Section
**Container Specifications:**
- Position: 0dp, 88dp (below status bar)
- Size: Screen width × 160dp
- Background: Linear gradient from Primary Red (#C62828) to Secondary Crimson (#D32F2F)
- Padding: 24dp all sides

**Profile Photo Container:**
- Position: 24dp, 24dp (from header top-left)
- Size: 80dp × 80dp
- Background: White circle
- Border: 3dp solid White
- Corner radius: 40dp (circular)
- Elevation: 2dp

**Profile Photo States:**
- **With photo:** User uploaded image, properly cropped
- **Placeholder:** person icon, 40dp, Warm Gray (#8D6E63)
- **Upload state:** camera_alt icon, 32dp, Primary Red (#C62828)

**User Information Layout:**
- Position: 120dp, 32dp (88dp + 32dp from left)
- Available width: Screen width - 144dp
- **Name:** H2 (24sp Bold), White
- **Role:** Body Medium (16sp), White with 80% opacity
- **Member since:** Body Small (14sp), White with 60% opacity
- **Verification status:** check_circle icon, 16dp, Success Emerald (#2E7D32)

#### Settings Sections (Grouped Layout)
**Section Container Specifications:**
- Position: 0dp, 248dp (below profile header)
- Size: Screen width × Variable height
- Background: Warm Cream (#FBF8F3)
- Section spacing: 24dp between groups

**Section Header Format:**
- Typography: H3 (20sp Medium), Deep Burgundy (#8E0000)
- Position: 16dp from left, 16dp top margin
- Bottom margin: 8dp before first setting item

#### Account Settings Section
**Container Height:** 240dp
**Setting Items Layout:**
- Item height: 56dp
- Padding: 16dp horizontal, 12dp vertical
- Border bottom: 1dp solid Soft Rose (#FFCDD2)

**Individual Setting Items:**
1. **Edit Profile:**
   - Icon: edit, 24dp, Primary Red (#C62828)
   - Label: "Edit Profile Information"
   - Action: Chevron right, 24dp, Warm Gray (#8D6E63)

2. **Change Password:**
   - Icon: lock, 24dp, Primary Red (#C62828)
   - Label: "Change Password"
   - Action: Chevron right, 24dp, Warm Gray (#8D6E63)

3. **Verification Documents:**
   - Icon: verified_user, 24dp, Success Emerald (#2E7D32)
   - Label: "Identity Verification"
   - Status: "Verified" badge, Success Emerald background

4. **Emergency Contacts:**
   - Icon: emergency, 24dp, Warning Amber (#FF8F00)
   - Label: "Emergency Contacts"
   - Count: "(2 contacts)" in Warm Gray

#### App Preferences Section
**Container Height:** 320dp

**Language & Region:**
- **Language Selection:**
  - Current: "English" with flag icon
  - Toggle: Switch to Urdu with immediate preview
  - RTL indicator: Text direction arrow icon

- **Region Settings:**
  - Location: "Pakistan" with country flag
  - Currency: "PKR" display format
  - Time zone: "Asia/Karachi" automatic detection

**Notification Controls:**
- **Master toggle:** Enable/disable all notifications
- **Granular controls:**
  - Transaction alerts: Toggle switch, Primary Red when enabled
  - Daily reminders: Time picker, 24-hour format
  - Emergency notifications: Always enabled, locked state
  - Marketing messages: Toggle switch, disabled by default

**Display Preferences:**
- **Theme selection:**
  - Light mode: Default, Warm Cream background preview
  - Dark mode: Deep Burgundy background preview
  - Auto: System setting sync

- **Text size:**
  - Slider control: Small → Medium → Large → Extra Large
  - Live preview: Sample text updates in real-time
  - Accessibility note: "Improves readability"

#### Privacy & Security Section
**Container Height:** 280dp

**Data Controls:**
- **Location sharing:**
  - Toggle: Share location with tandoors
  - Granularity: "Only when using app" vs "Always"
  - Privacy note: "Helps find nearby tandoors"

- **Usage analytics:**
  - Toggle: Share anonymous usage data
  - Purpose: "Helps improve app performance"
  - Opt-out: Clearly marked, no penalties

**Security Settings:**
- **Biometric authentication:**
  - Fingerprint: Available/Not available status
  - Face unlock: Available/Not available status
  - Setup button: Primary Red (#C62828) background

- **Login history:**
  - Recent sessions: Device, location, time
  - Suspicious activity: Alert indicators
  - Sign out all: Emergency security action

#### Help & Support Section
**Container Height:** 200dp

**Support Options:**
1. **FAQ & Help Center:**
   - Icon: help_center, 24dp, Primary Red (#C62828)
   - Label: "Frequently Asked Questions"
   - Badge: "Updated" if new content available

2. **Contact Support:**
   - Icon: support_agent, 24dp, Primary Red (#C62828)
   - Label: "Contact Customer Support"
   - Availability: "Available 9 AM - 6 PM PKT"

3. **Community Forum:**
   - Icon: forum, 24dp, Primary Red (#C62828)
   - Label: "Community Discussions"
   - Activity: "23 new posts" indicator

4. **Report Issue:**
   - Icon: bug_report, 24dp, Warning Amber (#FF8F00)
   - Label: "Report a Problem"
   - Quick access: Direct to issue form

#### Account Actions Section
**Container Height:** 120dp
**Critical Actions Layout:**

**Logout Button:**
- Position: 16dp from edges, 16dp from section top
- Size: Screen width - 32dp × 48dp
- Background: Soft Rose (#FFCDD2)
- Text: "Sign Out" in Deep Burgundy (#8E0000)
- Typography: Body Medium (16sp Medium)
- Corner radius: 8dp

**Delete Account Button:**
- Position: 16dp from edges, 80dp from section top
- Size: Screen width - 32dp × 48dp
- Background: Error Coral (#E57373) with 20% opacity
- Text: "Delete Account" in Error Coral (#E57373)
- Typography: Body Medium (16sp Medium)
- Corner radius: 8dp
- Confirmation: Requires password verification

### Role-Specific Settings

#### Laborer-Specific Settings
**Daily Preferences:**
- **Roti reminder time:** Time picker for daily allowance notification
- **Preferred tandoors:** Favorite locations for quick access
- **Walking distance:** Maximum distance willing to travel
- **Dietary restrictions:** Halal certification requirements

#### Tandoor Owner-Specific Settings
**Business Management:**
- **Operating hours:** Weekly schedule with holiday settings
- **QR code refresh:** Automatic vs manual regeneration
- **Staff permissions:** Role-based access for employees
- **Business verification:** License and permit uploads

#### Donor-Specific Settings
**Donation Preferences:**
- **Recurring donations:** Frequency and amount settings
- **Impact reports:** Email frequency for impact updates
- **Tax documentation:** Automatic receipt generation
- **Donation limits:** Monthly budget controls

### Accessibility Compliance
**WCAG 2.1 AA Standards:**
- **Color contrast:** All text meets 4.5:1 minimum ratio
- **Touch targets:** 56dp minimum for all interactive elements
- **Screen reader:** Semantic structure with proper headings
- **Keyboard navigation:** Tab order follows logical flow
- **Focus indicators:** 2dp outline in Secondary Crimson (#D32F2F)

**Cultural Accessibility:**
- **Language switching:** Immediate UI language change
- **RTL support:** Proper text direction for Urdu content
- **Cultural icons:** Locally relevant symbols and imagery
- **Respectful imagery:** Appropriate for Islamic context

---

## Design Prompt 9: Analytics & Reporting Screens

### Screen Category
**Business Intelligence & Impact Tracking Dashboard**

### Target User Types
- **Primary:** Tandoor Owners (business analytics)
- **Secondary:** Donors (impact reports)
- **Tertiary:** System Administrators (platform analytics)

### Layout Specifications (8dp Grid System)

#### Time Selector Header
**Container Specifications:**
- Position: 0dp, 88dp (below main header)
- Size: Screen width × 80dp
- Background: Warm Cream (#FBF8F3)
- Border bottom: 1dp solid Soft Rose (#FFCDD2)
- Padding: 16dp horizontal, 12dp vertical

**Time Range Selector:**
- Position: 16dp from left, vertically centered
- Layout: Horizontal scrolling chips
- Chip size: Variable width × 40dp
- Spacing: 8dp between chips
- Corner radius: 20dp (pill shape)

**Time Range Options:**
- **Today:** Default selection, Primary Red (#C62828) background
- **This Week:** Soft Rose (#FFCDD2) background
- **This Month:** Soft Rose (#FFCDD2) background
- **Last 3 Months:** Soft Rose (#FFCDD2) background
- **Custom Range:** Date picker integration

**Export Button:**
- Position: Screen width - 72dp, vertically centered
- Size: 56dp × 40dp
- Background: Secondary Crimson (#D32F2F)
- Icon: download, 20dp, White
- Corner radius: 20dp

#### Key Metrics Overview
**Container Specifications:**
- Position: 0dp, 168dp (below time selector)
- Size: Screen width × 200dp
- Background: Warm Cream (#FBF8F3)
- Padding: 16dp all sides

**Metrics Grid Layout:**
- Grid: 2 columns × 2 rows
- Cell size: (Screen width - 48dp) / 2 × 84dp
- Gap: 16dp horizontal, 16dp vertical

**Individual Metric Cards:**
- Background: White
- Border: 1dp solid Soft Rose (#FFCDD2)
- Corner radius: 12dp
- Elevation: 1dp
- Padding: 16dp

**Metric Card Content Structure:**
1. **Total Revenue Card:**
   - Icon: attach_money, 24dp, Success Emerald (#2E7D32)
   - Value: H2 (24sp Bold), Deep Burgundy (#8E0000)
   - Label: Body Small (14sp), Warm Gray (#8D6E63)
   - Trend: +15% ↗, Success Emerald (#2E7D32)

2. **Transactions Count:**
   - Icon: receipt, 24dp, Primary Red (#C62828)
   - Value: H2 (24sp Bold), Deep Burgundy (#8E0000)
   - Label: Body Small (14sp), Warm Gray (#8D6E63)
   - Trend: +8% ↗, Success Emerald (#2E7D32)

3. **Average Rating:**
   - Icon: star, 24dp, Warning Amber (#FF8F00)
   - Value: H2 (24sp Bold), Deep Burgundy (#8E0000)
   - Label: Body Small (14sp), Warm Gray (#8D6E63)
   - Trend: +0.2 ↗, Success Emerald (#2E7D32)

4. **Customer Retention:**
   - Icon: people, 24dp, Secondary Crimson (#D32F2F)
   - Value: H2 (24sp Bold), Deep Burgundy (#8E0000)
   - Label: Body Small (14sp), Warm Gray (#8D6E63)
   - Trend: -2% ↘, Warning Amber (#FF8F00)

#### Interactive Charts Section
**Container Specifications:**
- Position: 0dp, 384dp (below metrics)
- Size: Screen width × 400dp
- Background: White
- Padding: 16dp all sides
- Elevation: 2dp

**Chart Type Selector:**
- Position: 16dp from left, 16dp from top
- Layout: Horizontal tabs
- Tab size: Variable width × 40dp
- Active tab: Primary Red (#C62828) background, White text
- Inactive tabs: Soft Rose (#FFCDD2) background, Deep Burgundy text

**Chart Types:**
1. **Revenue Trends:** Line chart showing daily/weekly/monthly revenue
2. **Transaction Volume:** Bar chart showing transaction counts
3. **Peak Hours:** Heat map showing busiest times
4. **Customer Demographics:** Pie chart showing user distribution

**Chart Specifications:**
- **Chart area:** Screen width - 32dp × 280dp
- **Grid lines:** 1dp, Soft Rose (#FFCDD2)
- **Data lines:** 3dp width, Primary Red (#C62828)
- **Data points:** 8dp circles, Secondary Crimson (#D32F2F)
- **Hover states:** 12dp circles with tooltip
- **Axis labels:** Body Small (14sp), Warm Gray (#8D6E63)

#### Revenue Trends Chart (Line Chart)
**Visual Specifications:**
- **Primary line:** Revenue over time, 3dp width, Primary Red (#C62828)
- **Secondary line:** Subsidized vs regular sales, 2dp width, Secondary Crimson (#D32F2F)
- **Fill area:** Gradient from Primary Red to transparent
- **Data points:** Interactive with value tooltips
- **Y-axis:** Revenue in PKR with proper formatting
- **X-axis:** Time periods based on selected range

**Interactive Features:**
- **Zoom:** Pinch-to-zoom for detailed view
- **Pan:** Horizontal scrolling for extended periods
- **Tooltip:** Tap data points for exact values
- **Legend:** Toggle line visibility
- **Export:** Chart as image or data as CSV

#### Transaction Volume Chart (Bar Chart)
**Visual Specifications:**
- **Bar color:** Primary Red (#C62828) for regular transactions
- **Bar color:** Secondary Crimson (#D32F2F) for subsidized transactions
- **Bar width:** Calculated based on data density
- **Bar spacing:** 4dp between bars
- **Value labels:** Above bars, Body Small (14sp), Deep Burgundy

**Comparative Analysis:**
- **Stacked bars:** Show subsidized vs regular breakdown
- **Trend line:** Overall transaction trend overlay
- **Target line:** Monthly goals indicator
- **Variance indicators:** Above/below target markers

#### Data Tables Section
**Container Specifications:**
- Position: 0dp, 648dp (below charts)
- Size: Screen width × 152dp (height constrained by standard 800dp screen)
- Background: Warm Cream (#FBF8F3)
- Padding: 16dp horizontal

**Section Title:**
- Position: 16dp from left, 6dp from container top
- Typography: H3 (20sp Semi Bold), Deep Burgundy (#8E0000)
- Text: "Recent Transactions"
- Height: 24dp

**Summary Statistics Bar:**
- Position: 16dp from left, 32dp from container top
- Size: 328dp × 40dp
- Background: Primary Red (#C62828)
- Corner radius: 8dp
- Content layout: Horizontal distribution with 16dp internal padding
- Typography: Body Small (12sp Medium), White text
- Statistics displayed:
  - Total amount: "Total: PKR 165" (left aligned)
  - Transaction count: "2 Transactions" (center aligned)
- Overlaps with table header positioning for compact layout

**Table Header:**
- Position: 16dp from left, 30dp from container top
- Height: 48dp
- Background: Primary Red (#C62828)
- Text color: White
- Typography: Body Medium (14sp Medium)
- Corner radius: 8dp
- Column headers: Date/Time, Customer, Amount, Type, Status
- Sorting indicators: arrow_upward/arrow_downward, 16dp

**Table Rows:**
- Position: 16dp from left, 48dp from container top (immediately after header)
- Height: 56dp each
- Visible rows: 2 (due to 152dp height constraint)
- Additional rows: Preserved but positioned outside visible area
- Row 1: y:48dp-104dp (white background)
- Row 2: y:104dp-160dp (Soft Rose background with 10% opacity)
- Alternating backgrounds: White and Soft Rose (#FFCDD2) 10% opacity
- Border bottom: 1dp solid Soft Rose (#FFCDD2)
- Typography: Body Medium (14sp), Deep Burgundy (#8E0000)

**Table Columns (Tandoor Owner View):**
1. **Date/Time:** Transaction timestamp (14sp Regular)
2. **Customer ID:** Anonymized identifier (14sp Regular)
3. **Amount:** Transaction value in PKR (14sp Semi Bold)
4. **Type:** Subsidized/Regular indicator (12sp Medium)
   - Subsidized: Success Emerald (#2E7D32)
   - Regular: Primary Red (#C62828)
5. **Status:** Success/Failed/Pending (12sp Regular)
   - Success: ✅ Success (Success Emerald)
   - Failed: ❌ Failed (Error Coral #E57373)
   - Pending: ⏳ Pending (Warning Amber #FF8F00)

**Sample Data Implementation:**
- **Row 1:** 14:32, C-4521, PKR 45, Subsidized, ✅ Success
- **Row 2:** 13:45, C-4522, PKR 120, Regular, ✅ Success
- **Hidden Row 3:** 12:18, C-4523, PKR 25, Subsidized, ⏳ Pending
- **Hidden Row 4:** 11:52, C-4524, PKR 85, Regular, ❌ Failed

**Layout Constraints:**
- Maximum visible content: 152dp height
- Actual layout breakdown:
  - Title (6dp-30dp): 24dp
  - Summary bar (32dp-72dp): 40dp
  - Table header (30dp-78dp): 48dp
  - Transaction rows (48dp-160dp): 112dp for 2 rows
  - Total required: ~160dp (exceeds container by 8dp)
- Solution: Overlapping summary bar with header for compact design

#### Export & Sharing Features
**Export Options Modal:**
- **PDF Report:** Formatted business report with charts
- **CSV Data:** Raw data for external analysis
- **Image Export:** Charts as high-resolution images
- **Email Report:** Scheduled delivery options

**Sharing Controls:**
- **Internal sharing:** With staff members or partners
- **External sharing:** With accountants or investors
- **Privacy controls:** Data anonymization options
- **Access permissions:** View-only or full access

### Donor Impact Analytics

#### Impact Visualization Dashboard
**Container Specifications:**
- Position: 0dp, 88dp (below header)
- Size: Screen width × 320dp
- Background: Linear gradient from Primary Red (#C62828) to Secondary Crimson (#D32F2F)
- Padding: 24dp all sides

**Impact Counter Display:**
- Position: Center horizontal, 24dp from top
- Typography: H1 (48sp Bold), White
- Label: "Total Meals Provided"
- Counter animation: Incremental counting effect
- Supporting metrics: Families helped, communities reached

**Geographic Impact Map:**
- Position: 0dp, 408dp (below impact counter)
- Size: Screen width × 240dp
- Background: Warm Cream (#FBF8F3)
- Map style: Red-themed heat map
- Markers: Donation distribution points
- Intensity: Color-coded impact levels

#### Donation History Table
**Container Specifications:**
- Position: 0dp, 664dp (below map)
- Size: Screen width × Variable height
- Background: White
- Elevation: 1dp

**Table Columns (Donor View):**
1. **Date:** Donation timestamp
2. **Amount:** Donation value in PKR
3. **Method:** Payment method used
4. **Impact:** Meals provided calculation
5. **Receipt:** Download link for tax purposes

### Accessibility Features
**Screen Reader Support:**
- **Chart descriptions:** Detailed data summaries for non-visual users
- **Table navigation:** Proper header associations and row descriptions
- **Data announcements:** Live regions for dynamic content updates
- **Alternative formats:** Text-based data summaries

**Motor Accessibility:**
- **Large touch targets:** All interactive elements 56dp minimum
- **Gesture alternatives:** Voice commands for chart navigation
- **Simplified interactions:** Essential functions easily accessible
- **Keyboard navigation:** Full functionality without touch

**Visual Accessibility:**
- **High contrast mode:** Enhanced chart visibility
- **Text scaling:** Respects system font size settings
- **Color alternatives:** Pattern and texture coding beyond color
- **Data tables:** Alternative to visual charts

---

## Design Prompt 10: Notification & Communication Screens

### Screen Category
**System Alerts & User Messaging Interface**

### Layout Specifications (8dp Grid System)

#### Notification Center Header
**Container Specifications:**
- Position: 0dp, 88dp (below status bar)
- Size: Screen width × 80dp
- Background: Primary Red (#C62828)
- Padding: 16dp horizontal, 12dp vertical
- Elevation: 2dp

**Header Content Layout:**
- **Title:** "Notifications" H2 (24sp Bold), White
- **Unread count:** Badge with count, 24dp × 24dp, Warning Amber (#FF8F00)
- **Mark all read:** Button, Body Medium (16sp), White with 80% opacity
- **Settings icon:** settings, 24dp, White, 16dp from right edge

#### Notification Categories Filter
**Container Specifications:**
- Position: 0dp, 168dp (below header)
- Size: Screen width × 56dp
- Background: Warm Cream (#FBF8F3)
- Padding: 8dp horizontal

**Category Chips Layout:**
- Layout: Horizontal scrolling
- Chip height: 40dp
- Spacing: 8dp between chips
- Corner radius: 20dp (pill shape)

**Category Types:**
1. **All:** Default selection, Primary Red (#C62828) background
2. **Transactions:** Secondary Crimson (#D32F2F) when selected
3. **System:** Deep Burgundy (#8E0000) when selected
4. **Security:** Warning Amber (#FF8F00) when selected
5. **Social:** Success Emerald (#2E7D32) when selected

#### Notification List Container
**Container Specifications:**
- Position: 0dp, 224dp (below filters)
- Size: Screen width × (Screen height - 304dp)
- Background: Warm Cream (#FBF8F3)
- Scrolling: Vertical with pull-to-refresh

**Individual Notification Item:**
- Height: Variable (minimum 80dp)
- Background: White (unread) or Soft Rose (#FFCDD2) 10% opacity (read)
- Border bottom: 1dp solid Soft Rose (#FFCDD2)
- Padding: 16dp all sides

**Notification Item Layout:**
- **Icon area:** 48dp × 48dp, 16dp from left
- **Content area:** Remaining width - 32dp margin
- **Timestamp:** Top-right corner, Body Small (14sp)
- **Action area:** Bottom-right, 32dp height

#### Notification Types & Visual Design

#### Transaction Notifications
**Visual Specifications:**
- **Icon:** check_circle, 32dp, Success Emerald (#2E7D32)
- **Background:** Success Emerald (#2E7D32) 10% opacity circle
- **Title:** "Transaction Successful" H3 (20sp Medium), Deep Burgundy
- **Message:** Transaction details, Body Medium (16sp), Warm Gray
- **Amount:** Prominent display, H2 (24sp Bold), Primary Red (#C62828)
- **Action button:** "View Receipt" 120dp × 36dp, Secondary Crimson background

**Content Structure:**
- **Primary text:** "You purchased 3 roti for PKR 15"
- **Secondary text:** "At Ali's Tandoor, Gulberg"
- **Timestamp:** "2 minutes ago"
- **Quick actions:** View receipt, Rate experience

#### System Notifications
**Visual Specifications:**
- **Icon:** info, 32dp, Primary Red (#C62828)
- **Background:** Primary Red (#C62828) 10% opacity circle
- **Title:** System message title, H3 (20sp Medium), Deep Burgundy
- **Message:** System update details, Body Medium (16sp), Warm Gray
- **Action button:** "Learn More" 100dp × 36dp, Soft Rose background

**Content Examples:**
- **App updates:** "New features available in version 2.1"
- **Maintenance:** "Scheduled maintenance tonight 2-4 AM"
- **Policy changes:** "Updated privacy policy - please review"

#### Security Notifications
**Visual Specifications:**
- **Icon:** security, 32dp, Warning Amber (#FF8F00)
- **Background:** Warning Amber (#FF8F00) 15% opacity circle
- **Title:** Security alert title, H3 (20sp Medium), Deep Burgundy
- **Message:** Security details, Body Medium (16sp), Warm Gray
- **Urgency indicator:** Red dot, 8dp, Error Coral (#E57373)
- **Action button:** "Secure Account" 140dp × 36dp, Warning Amber background

**Content Examples:**
- **Login alerts:** "New login from unknown device"
- **Password changes:** "Password changed successfully"
- **Suspicious activity:** "Unusual account activity detected"

#### Social Notifications
**Visual Specifications:**
- **Icon:** people, 32dp, Success Emerald (#2E7D32)
- **Background:** Success Emerald (#2E7D32) 10% opacity circle
- **Title:** Social activity title, H3 (20sp Medium), Deep Burgundy
- **Message:** Community update, Body Medium (16sp), Warm Gray
- **Action button:** "View Community" 140dp × 36dp, Success Emerald background

**Content Examples:**
- **Community updates:** "New tandoor joined your area"
- **Achievements:** "You've helped provide 100 meals!"
- **Referrals:** "Friend joined using your referral code"

#### Notification Interaction States

#### Swipe Actions
**Left Swipe (Mark as Read/Unread):**
- **Swipe distance:** 80dp to trigger action
- **Background color:** Primary Red (#C62828)
- **Icon:** mark_email_read/mark_email_unread, 24dp, White
- **Animation:** Smooth slide with spring physics

**Right Swipe (Delete):**
- **Swipe distance:** 120dp to trigger action
- **Background color:** Error Coral (#E57373)
- **Icon:** delete, 24dp, White
- **Confirmation:** "Undo" snackbar for 5 seconds

#### Long Press Actions
**Context Menu Options:**
- **Mark as read/unread:** Toggle read status
- **Delete:** Remove notification
- **Archive:** Move to archived notifications
- **Block type:** Stop similar notifications
- **Share:** Forward notification content

#### Bulk Selection Mode
**Selection Interface:**
- **Activation:** Long press on notification item
- **Visual feedback:** Checkboxes appear, selected items highlighted
- **Action bar:** Bottom sheet with bulk actions
- **Actions:** Mark all read, Delete selected, Archive selected

#### Push Notification Settings

#### Notification Preferences Screen
**Container Specifications:**
- Position: 0dp, 88dp (below header)
- Size: Screen width × Variable height
- Background: Warm Cream (#FBF8F3)
- Sections: Grouped by notification type

**Master Controls:**
- **Enable notifications:** Master toggle switch
- **Do not disturb:** Time-based quiet hours
- **Sound settings:** Notification tone selection
- **Vibration patterns:** Haptic feedback options

**Granular Controls by Type:**
1. **Transaction Notifications:**
   - Toggle: Enable/disable transaction alerts
   - Sound: Custom tone selection
   - Frequency: Immediate, Batched hourly, Daily summary

2. **System Notifications:**
   - Toggle: Enable/disable system messages
   - Priority: High priority only, All messages
   - Delivery: Push, Email, Both

3. **Security Notifications:**
   - Toggle: Always enabled (locked setting)
   - Delivery: Immediate push notification
   - Backup: SMS for critical alerts

4. **Social Notifications:**
   - Toggle: Enable/disable community updates
   - Frequency: Real-time, Daily digest, Weekly summary
   - Content: Achievements only, All social activity

#### Real-Time Messaging Interface

#### Support Chat Integration
**Chat Container Specifications:**
- Position: 0dp, 88dp (below header)
- Size: Screen width × (Screen height - 168dp)
- Background: Warm Cream (#FBF8F3)
- Layout: Standard messaging interface

**Message Bubble Design:**
- **User messages:** Primary Red (#C62828) background, White text
- **Support messages:** White background, Deep Burgundy text
- **System messages:** Soft Rose (#FFCDD2) background, Warm Gray text
- **Corner radius:** 16dp with tail indicator
- **Padding:** 12dp horizontal, 8dp vertical

**Input Area:**
- **Position:** Bottom of screen, above keyboard
- **Height:** 56dp (expandable for multiline)
- **Background:** White with 1dp border in Soft Rose
- **Input field:** Full width minus send button
- **Send button:** 48dp × 48dp, Primary Red background, send icon

#### Emergency Communication
**Emergency Alert System:**
- **Activation:** Red emergency button in profile
- **Alert type:** High-priority push notification
- **Recipients:** Emergency contacts, support team
- **Content:** Location, user ID, timestamp
- **Follow-up:** Automatic check-in after 30 minutes

**Emergency Message Template:**
- **Subject:** "Emergency Alert - [User Name]"
- **Content:** "Emergency assistance requested. Location: [GPS coordinates]. Time: [timestamp]. User ID: [ID]"
- **Actions:** Call user, Send help, Mark resolved

### Accessibility Features
**Screen Reader Support:**
- **Notification announcements:** Live region updates for new notifications
- **Content descriptions:** Detailed descriptions for all notification types
- **Navigation:** Logical tab order through notification list
- **Actions:** Voice commands for common actions

**Visual Accessibility:**
- **High contrast mode:** Enhanced notification visibility
- **Text scaling:** Respects system font size settings
- **Color coding:** Multiple visual cues beyond color alone
- **Focus indicators:** Clear focus states for keyboard navigation

**Motor Accessibility:**
- **Large touch targets:** All interactive elements 56dp minimum
- **Gesture alternatives:** Voice commands for swipe actions
- **Simplified interactions:** Essential functions easily accessible
- **One-handed operation:** Primary actions within thumb reach

**Cultural Accessibility:**
- **Language support:** Notifications in user's preferred language
- **RTL layout:** Proper text direction for Urdu notifications
- **Cultural context:** Locally relevant notification content
- **Time formats:** 12/24 hour based on regional preferences

---

## Implementation Guidelines & Best Practices

### Design Token Structure
**Color Tokens:**
```
brand.primary.red: #C62828
brand.secondary.crimson: #D32F2F
brand.deep.burgundy: #8E0000
brand.warm.cream: #FBF8F3
brand.soft.rose: #FFCDD2
brand.warm.gray: #8D6E63

semantic.success: #2E7D32
semantic.warning: #FF8F00
semantic.error: #E57373

spacing.xs: 4dp
spacing.sm: 8dp
spacing.md: 16dp
spacing.lg: 24dp
spacing.xl: 32dp
spacing.xxl: 40dp
```

### Component Testing Checklist
**Accessibility Testing:**
- [ ] Screen reader navigation tested
- [ ] Keyboard navigation verified
- [ ] Color contrast validated
- [ ] Touch target sizes confirmed
- [ ] Focus indicators visible

**Cultural Testing:**
- [ ] Urdu RTL layout verified
- [ ] Cultural imagery appropriate
- [ ] Color meanings culturally sensitive
- [ ] Text expansion accommodated
- [ ] Local payment methods included

**Performance Testing:**
- [ ] Animation frame rate optimized
- [ ] Image compression verified
- [ ] Network efficiency tested
- [ ] Battery impact minimized
- [ ] Memory usage optimized

### Maintenance & Updates
**Regular Review Cycle:**
- **Monthly:** Accessibility compliance check
- **Quarterly:** Cultural sensitivity review
- **Bi-annually:** Color palette effectiveness
- **Annually:** Complete design system audit

**Feedback Integration:**
- User testing sessions with target demographics
- Developer feedback on implementation complexity
- Stakeholder reviews for business alignment
- Community input from actual users

This enhanced design documentation provides industry-leading specifications while maintaining the social impact mission and cultural sensitivity required for the Roti Meharbaan application serving Pakistani laborers, tandoor owners, and donors.
