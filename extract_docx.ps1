# PowerShell script to extract content from DOCX file
$docxFile = "Roti Meharbaan - Product Requirements Document (PRD).docx"
$tempDir = "temp_docx_extract"

Write-Host "Starting DOCX extraction..."

# Check if file exists
if (Test-Path $docxFile) {
    Write-Host "Found file: $docxFile"
    
    # Create temp directory
    if (Test-Path $tempDir) {
        Remove-Item $tempDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $tempDir | Out-Null
    
    # Extract docx as zip
    try {
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::ExtractToDirectory($docxFile, $tempDir)
        Write-Host "Extracted DOCX contents to $tempDir"
        
        # Check if document.xml exists
        $documentXml = Join-Path $tempDir "word\document.xml"
        if (Test-Path $documentXml) {
            Write-Host "Found document.xml"
            
            # Read and process XML
            [xml]$xmlContent = Get-Content $documentXml
            
            # Extract text content
            $textContent = @()
            
            # Get all text nodes
            $textNodes = $xmlContent.SelectNodes("//w:t", @{w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"})
            
            foreach ($node in $textNodes) {
                if ($node.InnerText) {
                    $textContent += $node.InnerText
                }
            }
            
            # Join all text
            $fullText = $textContent -join " "
            
            # Save to file
            $outputFile = "extracted_content.txt"
            $fullText | Out-File -FilePath $outputFile -Encoding UTF8
            
            Write-Host "Content extracted and saved to $outputFile"
            Write-Host "Content length: $($fullText.Length) characters"
            
            # Show preview
            Write-Host "`nPreview (first 500 characters):"
            Write-Host $fullText.Substring(0, [Math]::Min(500, $fullText.Length))
            
        } else {
            Write-Host "document.xml not found in extracted files"
        }
        
    } catch {
        Write-Host "Error extracting file: $_"
    }
    
    # Clean up temp directory
    if (Test-Path $tempDir) {
        Remove-Item $tempDir -Recurse -Force
    }
    
} else {
    Write-Host "File not found: $docxFile"
    Write-Host "Files in current directory:"
    Get-ChildItem | ForEach-Object { Write-Host "  - $($_.Name)" }
}

Write-Host "Script completed."
