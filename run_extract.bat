@echo off
echo Starting extraction...
python minimal_extract.py > output.log 2>&1
echo Extraction completed.
echo Checking for output files...
if exist content.txt (
    echo SUCCESS: content.txt created
    echo File size:
    for %%A in (content.txt) do echo %%~zA bytes
) else (
    echo ERROR: content.txt not found
)

if exist error.txt (
    echo ERROR file found:
    type error.txt
) else (
    echo No error file found
)

if exist success.txt (
    echo SUCCESS file found
) else (
    echo No success file found
)

echo.
echo Files in directory:
dir /b
pause
