import zipfile
import xml.etree.ElementTree as ET
import os

def extract_text_from_docx(docx_path):
    """Extract text from docx file using zipfile method"""
    try:
        with open("debug_log.txt", "w") as log:
            log.write(f"Starting to process: {docx_path}\n")
            
            if not os.path.exists(docx_path):
                log.write(f"File does not exist: {docx_path}\n")
                return None
            
            content = []
            
            with zipfile.ZipFile(docx_path, 'r') as docx_zip:
                log.write("Opened docx as zip file\n")
                
                # List all files in the zip
                file_list = docx_zip.namelist()
                log.write(f"Files in zip: {file_list}\n")
                
                # Read the main document
                if 'word/document.xml' in file_list:
                    document_xml = docx_zip.read('word/document.xml')
                    log.write("Read document.xml\n")
                    
                    root = ET.fromstring(document_xml)
                    log.write("Parsed XML\n")
                    
                    # Define namespace
                    namespace = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}
                    
                    # Extract text from paragraphs
                    para_count = 0
                    for para in root.findall('.//w:p', namespace):
                        para_text = ""
                        for text_elem in para.findall('.//w:t', namespace):
                            if text_elem.text:
                                para_text += text_elem.text
                        
                        if para_text.strip():
                            content.append(para_text.strip())
                            para_count += 1
                    
                    log.write(f"Extracted {para_count} paragraphs\n")
                    
                    # Extract text from tables
                    table_count = 0
                    for table in root.findall('.//w:tbl', namespace):
                        content.append(f"\n--- TABLE {table_count + 1} ---")
                        for row in table.findall('.//w:tr', namespace):
                            row_text = []
                            for cell in row.findall('.//w:tc', namespace):
                                cell_text = ""
                                for text_elem in cell.findall('.//w:t', namespace):
                                    if text_elem.text:
                                        cell_text += text_elem.text
                                row_text.append(cell_text.strip())
                            if any(row_text):
                                content.append(" | ".join(row_text))
                        content.append("--- END TABLE ---\n")
                        table_count += 1
                    
                    log.write(f"Extracted {table_count} tables\n")
                    
                    result = "\n".join(content)
                    log.write(f"Total content length: {len(result)} characters\n")
                    return result
                else:
                    log.write("document.xml not found in zip\n")
                    return None
                    
    except Exception as e:
        with open("error_log.txt", "w") as error_log:
            error_log.write(f"Error: {str(e)}\n")
            import traceback
            error_log.write(traceback.format_exc())
        return None

# Main execution
if __name__ == "__main__":
    docx_file = "Roti Meharbaan - Product Requirements Document (PRD).docx"
    
    with open("status.txt", "w") as status:
        status.write("Script started\n")
        
        if os.path.exists(docx_file):
            status.write(f"Found file: {docx_file}\n")
            
            content = extract_text_from_docx(docx_file)
            
            if content:
                # Save extracted content
                output_file = "extracted_content.txt"
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(content)
                status.write(f"Content extracted and saved to {output_file}\n")
                status.write(f"Content length: {len(content)} characters\n")
            else:
                status.write("Failed to extract content\n")
        else:
            status.write(f"File not found: {docx_file}\n")
            # List all files in current directory
            status.write("Files in current directory:\n")
            for file in os.listdir('.'):
                status.write(f"  - {file}\n")
        
        status.write("Script completed\n")
