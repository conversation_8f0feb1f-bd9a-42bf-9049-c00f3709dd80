# Roti Meharbaan - Product Requirements Document Analysis

## Document Overview

**Product Name:** <PERSON><PERSON><PERSON> (روٹی مہربان)  
**Version:** 1.0  
**Date:** July 2025  
**Document Type:** Mobile Application PRD  
**Platform:** Android (Primary), iOS (Future)

## Executive Summary

Roti Meharbaan is a social impact mobile application that provides subsidized bread (roti) to laborers (mazdoors) in Pakistan through a donor-funded ecosystem. The app connects charitable donors with tandoor owners to ensure affordable nutrition for the working class, addressing the critical issue where laborers spend 30-40% of their monthly income on basic food.

## Problem Statement

1. **Economic Challenge:** Laborers in Pakistan struggle to afford basic nutrition, with roti costing approximately 60 PKR for 3 pieces
2. **Income Impact:** With average earnings of 30K PKR per month, food costs represent 30-40% of total expenses
3. **Limited Access:** No structured donation systems for food assistance
4. **Lack of Transparency:** No transparency in charitable food distribution

## Solution Overview

A three-sided marketplace connecting:
- **Laborers (Mazdoors)** - Primary beneficiaries
- **Tandoor Owners** - Service providers  
- **Donors** - Funding sources

The solution uses QR code technology, geolocation services, and transparent payment systems to enable subsidized roti distribution (3 pieces for 5 PKR instead of 60 PKR).

## Key User Types Identified

### 1. Mazdoor (Laborer) - Primary Beneficiary
- **Demographics:** Blue-collar workers, construction workers, daily wage earners
- **Age Range:** 18-55 years
- **Income Level:** Low income (PKR 15,000-25,000/month)
- **Education:** Limited formal education, basic smartphone literacy
- **Languages:** Urdu primary, some English

### 2. Tandoor Owner - Service Provider
- **Demographics:** Tandoors located in housing societies or near residential areas
- **Business Type:** Traditional tandoor/bakery operations
- **Tech Literacy:** Moderate smartphone usage
- **Languages:** Urdu, some English

### 3. Donor - Funding Source
- **Demographics:** Middle to upper-middle class, philanthropists, corporate CSR
- **Education:** Well-educated, tech-savvy
- **Languages:** English primary, Urdu
- **Payment Scope:** Easypaisa, Debit/Credit card, offline account transfer

## Core Features Overview

The document outlines comprehensive features including:
- QR code-based transaction system
- Geolocation services for tandoor discovery
- Multi-language support (Urdu/English)
- Payment integration with multiple methods
- Analytics and reporting dashboards
- User verification systems
- Impact tracking and transparency features

## Document Structure Analysis

The PRD is organized into the following major sections:
1. Document Information & Product Overview
2. User Types & Requirements (detailed profiles for each user type)
3. Core Features & Functionality
4. Technical Requirements
5. UI/UX Specifications
6. Implementation Guidelines

This comprehensive PRD serves as the guide for Figma design creation and development implementation of the Roti Meharbaan mobile application, focusing on creating a sustainable ecosystem for affordable nutrition access in Pakistan.

---

# SECTION-BY-SECTION DEVELOPMENT PROMPTS

## PROMPT 1: User Registration & Authentication System

**Context:** Design and develop the complete user registration and authentication system for all three user types (Laborers, Tandoor Owners, Donors) in the Roti Meharbaan app.

**Requirements:**
- Create registration flows for three distinct user types with role-based access
- Implement multi-language support (Urdu/English) with RTL text support for Urdu
- Design simple, intuitive interfaces suitable for users with limited tech literacy
- Include phone number verification and basic profile setup
- Implement secure authentication with appropriate validation
- Create onboarding flows that explain the app's purpose and user benefits
- Design forms that accommodate both English and Urdu text input
- Include accessibility features for users with varying education levels

**Key Features to Include:**
- Role selection screen (Laborer/Tandoor Owner/Donor)
- Phone number verification with OTP
- Basic profile information collection (name, location, etc.)
- Terms of service and privacy policy acceptance
- Language preference selection
- Simple tutorial/walkthrough for first-time users

**Design Considerations:**
- Large, clear buttons and text for easy readability
- Visual icons to support text for low-literacy users
- Consistent color scheme and branding
- Responsive design for various Android screen sizes
- Offline capability for basic registration data storage

---

## PROMPT 2: QR Code System & Transaction Flow

**Context:** Develop the core QR code generation, scanning, and transaction processing system that enables subsidized roti distribution.

**Requirements:**
- Create dynamic QR code generation system for tandoor owners
- Implement QR code scanning functionality for laborers
- Design transaction validation and processing workflow
- Build real-time inventory tracking for daily roti limits
- Create secure payment processing integration
- Implement geolocation verification for transaction authenticity
- Design transaction history and receipt generation

**Key Features to Include:**
- QR code generation with daily expiration and refresh capability
- Camera-based QR scanner with validation feedback
- Transaction confirmation screens with clear pricing (5 PKR vs 60 PKR)
- Daily limit tracking (maximum 3 roti units per laborer)
- Real-time balance updates for donors and tandoor owners
- Transaction receipts with timestamp and location data
- Error handling for invalid QR codes or exceeded limits

**Technical Specifications:**
- QR codes should contain encrypted transaction data
- Integration with payment gateways (Easypaisa, card payments)
- GPS verification to ensure transactions occur at registered tandoor locations
- Offline transaction capability with sync when connection is restored
- Security measures to prevent QR code fraud or duplication

---

## PROMPT 3: Dashboard & Analytics System

**Context:** Create comprehensive dashboard interfaces for all user types with relevant analytics, tracking, and management features.

**Requirements:**
- Design role-specific dashboards with appropriate data visualization
- Implement real-time analytics and reporting features
- Create impact tracking and transparency tools for donors
- Build business intelligence tools for tandoor owners
- Design simple consumption tracking for laborers
- Include financial reporting and payment history

**Laborer Dashboard Features:**
- Daily roti consumption tracking
- Monthly savings calculator (showing money saved vs regular price)
- Nearby tandoor location finder with map integration
- Transaction history with clear visual indicators
- Remaining daily allowance display
- Simple, icon-based navigation

**Tandoor Owner Dashboard Features:**
- Daily sales analytics and revenue tracking
- QR code management (generate, refresh, print, share)
- Customer verification tools
- Inventory management for subsidized vs regular sales
- Payment tracking and settlement reports
- Business performance metrics and trends

**Donor Dashboard Features:**
- Impact visualization (number of meals provided, families helped)
- Donation history with detailed breakdowns
- Transparency reports showing fund utilization
- Tax documentation and receipt generation
- Recurring donation management
- Geographic impact mapping showing donation distribution

---

## PROMPT 4: Geolocation & Mapping System

**Context:** Develop location-based services for tandoor discovery, verification, and geographic impact tracking.

**Requirements:**
- Create interactive map interface for tandoor location discovery
- Implement GPS-based verification for transaction authenticity
- Build location registration system for tandoor owners
- Design proximity-based search and filtering
- Create geographic analytics for impact visualization
- Implement offline map capability for areas with poor connectivity

**Key Features to Include:**
- Interactive map showing nearby registered tandoors
- Distance calculation and route guidance
- Location-based search with filters (open hours, availability)
- GPS verification during QR code transactions
- Tandoor registration with precise location coordinates
- Geographic heat maps showing donation impact distribution
- Offline map data caching for essential locations

**Technical Considerations:**
- Integration with Google Maps or similar mapping service
- GPS accuracy requirements for transaction validation
- Geofencing capabilities for tandoor location verification
- Location privacy settings and user consent management
- Battery optimization for continuous location services

---

## PROMPT 5: Payment Integration & Financial Management

**Context:** Build comprehensive payment processing system supporting multiple payment methods and financial tracking.

**Requirements:**
- Integrate multiple payment gateways (Easypaisa, debit/credit cards)
- Create secure payment processing with fraud prevention
- Implement wallet system for donors and tandoor owners
- Design payment history and financial reporting
- Build automated settlement and reconciliation systems
- Create tax documentation and compliance features

**Payment Features:**
- Multiple payment method support (mobile wallets, cards, bank transfers)
- Secure payment processing with encryption
- Real-time payment confirmation and receipt generation
- Automated fund distribution to tandoor owners
- Payment scheduling for recurring donations
- Currency conversion and multi-currency support if needed

**Financial Management:**
- Digital wallet system with balance tracking
- Transaction categorization and reporting
- Automated tax calculation and documentation
- Financial analytics and spending insights
- Settlement reports for tandoor owners
- Audit trails for all financial transactions

---

## PROMPT 6: Notification & Communication System

**Context:** Develop comprehensive notification and communication system to keep all users informed and engaged.

**Requirements:**
- Create multi-channel notification system (push, SMS, in-app)
- Design user preference management for notifications
- Implement real-time updates for transactions and system events
- Build communication channels between user types
- Create emergency notification system
- Design engagement and retention notifications

**Notification Types:**
- Transaction confirmations and receipts
- Daily limit reminders for laborers
- QR code expiration alerts for tandoor owners
- Donation impact updates for donors
- System maintenance and update notifications
- Security alerts and fraud prevention notices

**Communication Features:**
- In-app messaging for support and queries
- Feedback and rating system for tandoor services
- Community features for user engagement
- Multi-language notification support
- Notification scheduling and frequency management
- Emergency broadcast system for critical updates

---

## PROMPT 7: Security & Fraud Prevention System

**Context:** Implement comprehensive security measures to prevent fraud and ensure system integrity.

**Requirements:**
- Design user verification and identity management system
- Create fraud detection and prevention mechanisms
- Implement secure data storage and transmission
- Build audit trails and monitoring systems
- Design access control and permission management
- Create incident response and security monitoring

**Security Features:**
- Multi-factor authentication for sensitive operations
- Biometric verification options where available
- QR code encryption and anti-tampering measures
- Transaction monitoring for suspicious patterns
- User behavior analytics for fraud detection
- Secure API endpoints with rate limiting

**Data Protection:**
- End-to-end encryption for sensitive data
- GDPR-compliant data handling and storage
- User privacy controls and data deletion options
- Secure backup and disaster recovery systems
- Regular security audits and penetration testing
- Compliance with local data protection regulations

---

## PROMPT 8: Admin Panel & System Management

**Context:** Develop comprehensive administrative interface for system management, monitoring, and support.

**Requirements:**
- Create admin dashboard with system-wide analytics
- Build user management and support tools
- Implement system configuration and settings management
- Design reporting and compliance tools
- Create monitoring and alerting systems
- Build content management and localization tools

**Admin Features:**
- System-wide analytics and performance monitoring
- User account management and verification tools
- Tandoor registration approval and management
- Financial transaction monitoring and reconciliation
- Content management for app text and notifications
- System configuration and feature flag management

**Support Tools:**
- Customer support ticket system
- User communication and broadcast tools
- Issue tracking and resolution workflows
- Knowledge base and FAQ management
- Training materials and documentation
- System health monitoring and alerting

---

## PROMPT 9: Offline Capability & Data Synchronization

**Context:** Design robust offline functionality to ensure app usability in areas with poor internet connectivity.

**Requirements:**
- Create offline transaction processing capability
- Implement data synchronization when connectivity is restored
- Design local data storage and caching systems
- Build conflict resolution for offline/online data discrepancies
- Create offline map and location services
- Implement progressive data loading and optimization

**Offline Features:**
- Local transaction storage with automatic sync
- Cached map data for essential locations
- Offline QR code validation with later verification
- Local user profile and preference storage
- Offline notification queuing and delivery
- Essential app functionality without internet connection

**Synchronization:**
- Intelligent data sync prioritization
- Conflict resolution for simultaneous offline changes
- Bandwidth-optimized sync protocols
- Background synchronization with user control
- Sync status indicators and user feedback
- Data integrity verification after sync

---

## PROMPT 10: Testing, Deployment & Maintenance

**Context:** Establish comprehensive testing, deployment, and ongoing maintenance procedures for the application.

**Requirements:**
- Create testing strategy for all user types and scenarios
- Design deployment pipeline for Android and future iOS
- Implement monitoring and analytics for production environment
- Create maintenance and update procedures
- Design user feedback collection and analysis
- Build performance optimization and scaling strategies

**Testing Strategy:**
- Unit testing for all core functionality
- Integration testing for payment and external services
- User acceptance testing with actual target users
- Security testing and vulnerability assessments
- Performance testing under various load conditions
- Accessibility testing for users with disabilities

**Deployment & Maintenance:**
- Continuous integration and deployment pipeline
- A/B testing framework for feature optimization
- Real-time monitoring and error tracking
- User analytics and behavior tracking
- Regular security updates and patches
- Feature rollout and rollback capabilities

This comprehensive set of prompts covers all major aspects of the Roti Meharbaan application as outlined in the PRD, providing detailed guidance for design and development teams to create a robust, user-friendly, and impactful social application.
