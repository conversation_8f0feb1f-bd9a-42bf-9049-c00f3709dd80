# Roti Meharbaan Mobile App - Master Figma Design Specifications
## Comprehensive UI/UX Design System for Pakistani Food Delivery & Social Impact Platform

### Table of Contents
1. [Design System Foundation](#design-system-foundation)
2. [User Onboarding & Registration](#user-onboarding--registration)
3. [Laborer Dashboard & QR Scanning](#laborer-dashboard--qr-scanning)
4. [Tandoor Owner Business Interface](#tandoor-owner-business-interface)
5. [Donor Impact & Donation Flows](#donor-impact--donation-flows)
6. [Payment & Transaction Screens](#payment--transaction-screens)
7. [Profile & Settings Pages](#profile--settings-pages)
8. [Map & Location Services](#map--location-services)
9. [Analytics & Reporting Dashboards](#analytics--reporting-dashboards)
10. [Component Library & Interactions](#component-library--interactions)

---
prompt1:
## Design System Foundation

Create a comprehensive foundational design system in Figma for "Roti Meharbaan" – a culturally-sensitive Pakistani food delivery and social impact mobile app connecting tandoor owners, laborers, and donors. This design system must serve as the authoritative source for all subsequent screen designs and maintain consistency across the entire application ecosystem.

### Color Palette (Updated Burnt Orange Theme)
**Primary Colors:**
- 🔶 **Primary Burnt Orange:** #E67E22 (WCAG contrast: 4.8:1 on cream)
- 🟤 **Secondary Golden Brown:** #C49E69 (WCAG contrast: 5.1:1 on cream)
- ⚫ **Accent Charcoal:** #333333 (WCAG contrast: 12.6:1 on cream)

**Background & Surface System:**
- ☁️ **Background Cream:** #FAF3E0 – primary background optimized for outdoor visibility
- 🏖️ **Surface Tan:** #F5E1A4 – card surfaces, subtle accents, disabled states
- 🌰 **Text Warm Gray:** #8D6E63 (WCAG contrast: 4.6:1 on cream)

**Functional Colors:**
- ✅ **Success Sage:** #27AE60 (WCAG contrast: 4.9:1)
- ❌ **Error Coral:** #E57373 (WCAG contrast: 4.1:1)
- ⚠️ **Warning Orange:** #E67E22 (using primary color)
- 🔵 **Information Blue:** #3498DB (WCAG contrast: 4.5:1)

### Bilingual Typography System

#### English Typography (Noto Sans)
**Font Family:** Noto Sans
**Character Set:** Latin, Extended Latin, Punctuation
**Font Weights:** Regular (400), Medium (500), Bold (700)

- **H1 Display:** 48sp Bold, line-height 56sp (hero numbers, impact metrics)
- **H1 Headline:** 32sp Bold, line-height 40sp (page titles, primary headings)
- **H2 Headline:** 24sp Bold, line-height 32sp (section headers, card titles)
- **H3 Headline:** 20sp Medium, line-height 28sp (subsection headers)
- **Body Large:** 18sp Regular, line-height 28sp (important descriptions)
- **Body Medium:** 16sp Regular, line-height 24sp (standard content)
- **Body Small:** 14sp Regular, line-height 20sp (metadata, captions)
- **Caption:** 12sp Regular, line-height 16sp (timestamps, fine print)

#### Urdu Typography (Noto Nastaliq Urdu)
**Font Family:** Noto Nastaliq Urdu
**Character Set:** Arabic, Urdu, Persian, Punctuation
**Font Weights:** Regular (400), Bold (700)
**Text Direction:** RTL (Right-to-Left)

- **H1 Display:** 52sp Bold, line-height 64sp (+8% size increase for Urdu readability)
- **H1 Headline:** 36sp Bold, line-height 48sp (+12% size increase)
- **H2 Headline:** 28sp Bold, line-height 40sp (+16% size increase)
- **H3 Headline:** 24sp Bold, line-height 36sp (+20% size increase)
- **Body Large:** 20sp Regular, line-height 32sp (+11% size increase)
- **Body Medium:** 18sp Regular, line-height 28sp (+12% size increase)
- **Body Small:** 16sp Regular, line-height 24sp (+14% size increase)
- **Caption:** 14sp Regular, line-height 20sp (+16% size increase)

#### Bilingual Layout Considerations
**Text Expansion Rules:**
- **Urdu Content:** Allocate 30-40% additional horizontal space
- **Vertical Spacing:** Increase line-height by 15% for Urdu text
- **Container Width:** Dynamic width adjustment based on language
- **Truncation:** Proper ellipsis handling for both languages

**RTL Layout Specifications:**
- **Text Alignment:** Right-aligned for Urdu, left-aligned for English
- **Icon Positioning:** Mirror horizontally for RTL (chevrons, arrows, navigation)
- **Layout Flow:** Reverse horizontal order of elements for Urdu
- **Margin/Padding:** Swap left/right values for RTL layouts

### Grid System & Spacing
**8dp Baseline Grid:**
- **Base Unit:** 8dp
- **Micro Spacing:** 4dp (fine adjustments)
- **Standard Spacing:** 8dp, 16dp, 24dp, 32dp
- **Macro Spacing:** 40dp, 48dp, 56dp, 64dp

**Component Spacing Rules:**
- **Text to container edge:** 16dp minimum
- **Between related elements:** 8dp
- **Between unrelated elements:** 24dp
- **Section separators:** 32dp
- **Screen margins:** 16dp (mobile), 24dp (tablet)

### Elevation System
**Material Design Elevation with Burnt Orange Tints:**
- **0dp:** Flat surfaces (no shadow) - Background Cream (#FAF3E0)
- **2dp:** Cards, buttons (subtle shadow) - Surface Tan (#F5E1A4) with light Burnt Orange tint
- **4dp:** App bars, tabs (medium shadow) - Enhanced Burnt Orange (#E67E22) shadow
- **8dp:** Dialogs, menus (prominent shadow) - Strong Burnt Orange shadow with blur

### Touch Target Standards
- **Minimum:** 48dp × 48dp (WCAG AA compliance)
- **Recommended:** 56dp × 56dp for primary actions
- **Work Environment:** 64dp × 64dp for outdoor use with gloves
- **Adjacent Spacing:** 8dp minimum between targets

### Comprehensive Bilingual Implementation Guidelines

#### Language Switching Mechanism
**Global Language Toggle:**
- **Position:** Top-right corner of all screens (X: 312dp, Y: 8dp)
- **Dimensions:** 32dp × 32dp
- **Icon:** language, 20dp, Charcoal (#333333)
- **Behavior:** Instant language switch with layout animation (300ms)
- **Persistence:** Language preference saved in local storage
- **Default:** English for new users, system language detection available

#### RTL Layout Transformation Rules
**Horizontal Mirroring:**
- **Navigation Elements:** Back buttons, chevrons, arrows flip horizontally
- **Text Alignment:** Left-aligned English becomes right-aligned Urdu
- **Icon Positioning:** Icons move from left to right side of text
- **Layout Flow:** Element order reverses (first element becomes last)
- **Margins/Padding:** Left margins become right margins and vice versa

**Elements That Don't Mirror:**
- **Numbers:** Maintain LTR direction (123, not ۳۲۱)
- **Brand Logos:** Keep original orientation
- **Maps:** Geographic orientation remains constant
- **Charts/Graphs:** Data visualization maintains standard orientation
- **QR Codes:** Always maintain standard orientation

#### Text Expansion Handling
**Urdu Text Requirements:**
- **Horizontal Expansion:** 30-40% additional width for Urdu text
- **Vertical Expansion:** 15-20% additional height for proper line spacing
- **Container Adaptation:** Dynamic width adjustment based on content language
- **Truncation Strategy:** Proper ellipsis handling with language-appropriate breaks
- **Multi-line Support:** Enhanced line-height for Urdu readability

#### Cultural Localization Standards
**Islamic Context Integration:**
- **Greetings:** "Assalam Alaikum" used in both languages
- **Time References:** Prayer time awareness in scheduling
- **Currency:** PKR formatting with Urdu numerals option
- **Dates:** Islamic calendar support alongside Gregorian
- **Terminology:** Respectful business and social terminology

**Pakistani Business Culture:**
- **Formal Address:** Respectful titles and honorifics
- **Family Context:** Recognition of family-oriented decisions
- **Community Focus:** Emphasis on social responsibility and helping others
- **Professional Hierarchy:** Appropriate respect for authority and experience

#### Font Loading and Fallback Strategy
**English Font Stack:**
```css
font-family: 'Noto Sans', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
```

**Urdu Font Stack:**
```css
font-family: 'Noto Nastaliq Urdu', 'Jameel Noori Nastaleeq', 'Alvi Nastaleeq', serif;
```

**Loading Strategy:**
- **Primary:** Load Noto fonts with font-display: swap
- **Fallback:** System fonts while custom fonts load
- **Offline:** Cached fonts with system fallback
- **Performance:** Subset fonts for required characters only

#### Unicode and Character Support
**Urdu Character Set:**
- **Basic Arabic:** U+0600-U+06FF
- **Arabic Supplement:** U+0750-U+077F
- **Arabic Extended-A:** U+08A0-U+08FF
- **Urdu Specific:** Additional diacritics and ligatures
- **Punctuation:** Both Arabic and Latin punctuation marks

**Text Processing:**
- **Bidirectional Text:** Proper handling of mixed LTR/RTL content
- **Ligature Support:** Automatic ligature formation for Urdu
- **Diacritic Positioning:** Correct placement of Urdu diacritics
- **Word Breaking:** Language-appropriate line breaking rules

---
prompt2:
## User Onboarding & Registration

### Screen 1: Welcome Splash Screen
**Canvas Size:** 360dp × 800dp | **Background:** Background Cream (#FAF3E0)

#### App Logo Section (0dp, 120dp → 360dp, 280dp)
**App Logo Section (0dp, 120dp → 360dp, 280dp):**
- Logo Container: Position X: 130dp, Y: 140dp (centered)
- Dimensions: 100dp × 100dp, Surface Tan (#F5E1A4) circle
- Corner Radius: 50dp, Elevation: 4dp with Burnt Orange shadow
- Icon: Custom roti/tandoor icon, 60dp, Burnt Orange (#E67E22)

**App Name Typography (Bilingual):**
- Position: X: 80dp, Y: 260dp
- **Text:** "Roti Meharbaan"
- Typography: H1 Headline (32sp Bold English / 36sp Bold Urdu), Charcoal (#333333)
- Line Height:  40sp, Text Align: Center, Max Width: 200dp
- Max Width: 200dp English / 260dp Urdu (30% expansion)

#### Tagline Section (0dp, 280dp → 360dp, 380dp)
- Primary Tagline0: Connecting Communities Through Bread
- Position: X: 32dp, Y: 300dp
- **Text:** "Connecting Communities Through Bread"
- Typography: Body Large (18sp Regular), Warm Gray (#8D6E63)
- Line Height:  24sp, Text Align: Center, Max Width: 296dp
- Position: X: 32dp, Y: 340dp
- Typography: Body Medium (16sp Regular), Warm Gray (#8D6E63)
- Line Height: 24sp, Text Align: Center, Max Width: 296dp

**Language Selection (0dp, 420dp → 360dp, 520dp)**

- English Button: Position X: 48dp, Y: 440dp, Dimensions: 128dp × 56dp
- Background: Burnt Orange (#E67E22) [Active State], Corner Radius: 8dp
- Text: "English", Typography: Body Medium (16sp Medium), White
- Urdu Button: Position X: 184dp, Y: 440dp, Dimensions: 128dp × 56dp
- Background: Surface Tan (#F5E1A4) [Inactive State], Corner Radius: 8dp
- Text: "اردو", Typography: Body Medium (16sp Medium), Charcoal (#333333)
- Continue Button (0dp, 600dp → 360dp, 720dp)

**Position: X: 32dp, Y: 632dp, Dimensions: 296dp × 56dp**
- Background: Burnt Orange (#E67E22), Corner Radius: 8dp, Elevation: 2dp
- Text: "Get Started", Typography: Body Medium (16sp Medium), White
- Screen 2: Role Selection Screen (English)
- Canvas Size: 360dp × 800dp | Background: Background Cream (#FAF3E0)

### Header Section (0dp, 44dp → 360dp, 140dp)

- Back Button: Position X: 16dp, Y: 60dp, Dimensions: 48dp × 48dp
- Icon: arrow_back, 24dp, Charcoal (#333333)
- Title: "Choose Your Role"
- Position: X: 80dp, Y: 70dp
- Typography: H2 Headline (24sp Bold), Charcoal (#333333), Left aligned
- Subtitle: "How will you use Roti Meharbaan?"
- Position: X: 80dp, Y: 102dp
- Typography: Body Medium (16sp Regular), Warm Gray (#8D6E63), Left aligned
- Max Width: 264dp

### Role Cards Section (0dp, 140dp → 360dp, 680dp)

- Grid: 1 column × 4 rows, Card Dimensions: 328dp × 120dp each
- Vertical Spacing: 16dp between cards, Padding: 16dp all sides

### Card 1: Laborer (16dp, 156dp → 344dp, 276dp)

- Background: Surface Tan (#F5E1A4), Corner Radius: 12dp, Elevation: 1dp
- Icon: Position X: 32dp, Y: 172dp, 48dp × 48dp
- Background: Burnt Orange (#E67E22) circle, Icon: construction_worker, 32dp, White
- Title: "Laborer", Position X: 96dp, Y: 180dp
- Typography: H3 Headline (20sp Medium), Charcoal (#333333), Left aligned
- Description: "Access daily subsidized roti", Position X: 96dp, Y: 204dp
- Typography: Body Medium (16sp Regular), Warm Gray (#8D6E63), Left aligned
- Badge: "Daily allowance tracking", Position X: 96dp, Y: 228dp
- Typography: Caption (12sp Regular), Success Sage (#27AE60), Left aligned

### Card 2: Tandoor Owner (16dp, 292dp → 344dp, 412dp)

- Same container styling as Laborer card
- Icon: Background Golden Brown (#C49E69) circle, Icon: store, 32dp, White
- Title: "Tandoor Owner"
- Description: "Manage your tandoor business"
- Badge: "Analytics & QR management"

### Card 3: Donor (16dp, 428dp → 344dp, 548dp)

- Same container styling as Laborer card
- Icon: Background Success Sage (#27AE60) circle, Icon: favorite, 32dp, White
- Title: "Donor"
- Description: "Support community meals"
- Badge: "Track your impact"

### Card 4: Delivery Personnel (16dp, 564dp → 344dp, 684dp)

- Same container styling as Laborer card
- Icon: Background Charcoal (#333333) circle, Icon: delivery_dining, 32dp, White
- Title: "Delivery Personnel"
- Description: "Deliver meals efficiently"
- Badge: "Route optimization"
---------------------------------------

# urdu:
صارف کی رجسٹریشن اور داخلہ (اردو)
اسکرین 1: خوش آمدید اسپلیش اسکرین (اردو)
کینوس سائز: 360dp × 800dp | پس منظر: Background Cream (#FAF3E0)

ایپ لوگو سیکشن (0dp, 120dp → 360dp, 280dp)

لوگو کنٹینر: پوزیشن X: 130dp, Y: 140dp (مرکز میں)
ابعاد: 100dp × 100dp, Surface Tan (#F5E1A4) دائرہ
کونے کا ریڈیس: 50dp, بلندی: 4dp Burnt Orange سائے کے ساتھ
آئیکن: کسٹم روٹی/تندور آئیکن، 60dp, Burnt Orange (#E67E22)
ایپ نام ٹائپوگرافی:

پوزیشن: X: 230dp, Y: 260dp (RTL کے لیے)
متن: "روٹی مہربان"
ٹائپوگرافی: H1 Headline (36sp Bold), Charcoal (#333333)
لائن کی اونچائی: 48sp, متن سیدھ: مرکز، زیادہ سے زیادہ چوڑائی: 260dp
ٹیگ لائن سیکشن (0dp, 280dp → 360dp, 380dp)

بنیادی ٹیگ لائن: "روٹی کے ذریعے برادریوں کو جوڑنا"
پوزیشن: X: 32dp, Y: 300dp
ٹائپوگرافی: Body Large (20sp Regular), Warm Gray (#8D6E63)
لائن کی اونچائی: 32sp, متن سیدھ: مرکز، زیادہ سے زیادہ چوڑائی: 385dp
ذیلی عنوان: "محنت کش لوگوں کے لیے باعزت کھانا"
پوزیشن: X: 32dp, Y: 340dp
ٹائپوگرافی: Body Medium (18sp Regular), Warm Gray (#8D6E63)
لائن کی اونچائی: 28sp, متن سیدھ: مرکز، زیادہ سے زیادہ چوڑائی: 385dp
زبان کا انتخاب (0dp, 420dp → 360dp, 520dp)

اردو بٹن: پوزیشن X: 48dp, Y: 440dp, ابعاد: 128dp × 56dp
پس منظر: Burnt Orange (#E67E22) [فعال حالت], کونے کا ریڈیس: 8dp
متن: "اردو", ٹائپوگرافی: Body Medium (18sp Medium Noto Nastaliq Urdu), سفید
انگریزی بٹن: پوزیشن X: 184dp, Y: 440dp, ابعاد: 128dp × 56dp
پس منظر: Surface Tan (#F5E1A4) [غیر فعال حالت], کونے کا ریڈیس: 8dp
متن: "انگریزی", ٹائپوگرافی: Body Medium (16sp Medium), Charcoal (#333333)
جاری رکھیں بٹن (0dp, 600dp → 360dp, 720dp)

پوزیشن: X: 32dp, Y: 632dp, ابعاد: 296dp × 56dp
پس منظر: Burnt Orange (#E67E22), کونے کا ریڈیس: 8dp, بلندی: 2dp
متن: "شروع کریں", ٹائپوگرافی: Body Medium (18sp Medium), سفید
متن سیدھ: دائیں (RTL کے لیے)
اسکرین 2: کردار کا انتخاب اسکرین (اردو)
کینوس سائز: 360dp × 800dp | پس منظر: Background Cream (#FAF3E0)

ہیڈر سیکشن (0dp, 44dp → 360dp, 140dp)

واپس بٹن: پوزیشن X: 312dp, Y: 60dp, ابعاد: 48dp × 48dp (RTL کے لیے)
آئیکن: arrow_forward, 24dp, Charcoal (#333333) (RTL کے لیے)
عنوان: "اپنا کردار منتخب کریں"
پوزیشن: X: 264dp, Y: 70dp (RTL کے لیے)
ٹائپوگرافی: H2 Headline (28sp Bold), Charcoal (#333333), دائیں سیدھ
ذیلی عنوان: "آپ روٹی مہربان کا استعمال کیسے کریں گے؟"
پوزیشن: X: 264dp, Y: 102dp (RTL کے لیے)
ٹائپوگرافی: Body Medium (18sp Regular), Warm Gray (#8D6E63), دائیں سیدھ
زیادہ سے زیادہ چوڑائی: 343dp
کردار کارڈز سیکشن (0dp, 140dp → 360dp, 680dp)

گرڈ: 1 کالم × 4 قطاریں، کارڈ ابعاد: 328dp × 120dp ہر ایک
عمودی فاصلہ: کارڈز کے درمیان 16dp، پیڈنگ: تمام اطراف 16dp
کارڈ 1: مزدور (16dp, 156dp → 344dp, 276dp)

پس منظر: Surface Tan (#F5E1A4), کونے کا ریڈیس: 12dp, بلندی: 1dp
آئیکن: پوزیشن X: 296dp, Y: 172dp, 48dp × 48dp (RTL کے لیے)
پس منظر: Burnt Orange (#E67E22) دائرہ، آئیکن: construction_worker, 32dp, سفید
عنوان: "مزدور", پوزیشن X: 232dp, Y: 180dp (RTL کے لیے)
ٹائپوگرافی: H3 Headline (24sp Bold), Charcoal (#333333), دائیں سیدھ
تفصیل: "یومیہ سبسڈی والی روٹی حاصل کریں", پوزیشن X: 232dp, Y: 204dp
ٹائپوگرافی: Body Medium (18sp Regular), Warm Gray (#8D6E63), دائیں سیدھ
بیج: "یومیہ الاؤنس ٹریکنگ", پوزیشن X: 232dp, Y: 228dp
ٹائپوگرافی: Caption (14sp Regular), Success Sage (#27AE60), دائیں سیدھ
کارڈ 2: تندور مالک (16dp, 292dp → 344dp, 412dp)

مزدور کارڈ جیسی کنٹینر اسٹائلنگ
آئیکن: پس منظر Golden Brown (#C49E69) دائرہ، آئیکن: store, 32dp, سفید
عنوان: "تندور مالک"
تفصیل: "اپنے تندور کاروبار کا انتظام کریں"
بیج: "تجزیات اور QR انتظام"
کارڈ 3: عطیہ دہندہ (16dp, 428dp → 344dp, 548dp)

مزدور کارڈ جیسی کنٹینر اسٹائلنگ
آئیکن: پس منظر Success Sage (#27AE60) دائرہ، آئیکن: favorite, 32dp, سفید
عنوان: "عطیہ دہندہ"
تفصیل: "کمیونٹی کے کھانے میں مدد کریں"
بیج: "اپنے اثرات کو ٹریک کریں"
کارڈ 4: ڈیلیوری عملہ (16dp, 564dp → 344dp, 684dp)

مزدور کارڈ جیسی کنٹینر اسٹائلنگ
آئیکن: پس منظر Charcoal (#333333) دائرہ، آئیکن: delivery_dining, 32dp, سفید
عنوان: "ڈیلیوری عملہ"
تفصیل: "مؤثر طریقے سے کھانا پہنچائیں"
بیج: "راستے کی بہتری"

---
promt2:
# 📱 ENGLISH VERSION PROMPT
**LABORER DASHBOARD & QR SCANNING (ENGLISH VERSION)**
**Screen 1: Laborer Dashboard (English)**
Canvas Size: 360dp × 800dp | Background: Background Cream (#FAF3E0)

Top App Bar (0dp, 44dp → 360dp, 108dp)
Container:

Position: X: 0dp, Y: 44dp
Dimensions: 360dp × 64dp
Background: Burnt Orange (#E67E22)
Elevation: 4dp with enhanced shadow
Greeting Section:

Position: X: 16dp, Y: 60dp
Text: "Assalam Alaikum, Ahmad"
Typography: H3 Headline (20sp Medium), White
Line Height: 28sp
Text Alignment: Left
Profile Icon:

Position: X: 280dp, Y: 68dp
Dimensions: 32dp × 32dp
Icon: person, 24dp, White
Touch Target: 48dp × 48dp
Action: Navigate to "My Profile" screen
Accessibility Label: "Open profile"
Notification Icon:

Position: X: 312dp, Y: 68dp
Dimensions: 32dp × 32dp
Icon: notifications, 24dp, White
Touch Target: 48dp × 48dp
Badge: 8dp circle, Error Coral (#E57373) if notifications present
Accessibility Label: "View notifications"
Daily Allowance Card (16dp, 124dp → 344dp, 268dp)
Container:

Position: X: 16dp, Y: 124dp
Dimensions: 328dp × 144dp
Background: Surface Tan (#F5E1A4)
Corner Radius: 16dp, Elevation: 2dp
Padding: 20dp all sides
Allowance Counter:

Position: X: 36dp, Y: 144dp
Dimensions: 120dp × 104dp
Background: Burnt Orange (#E67E22)
Corner Radius: 12dp, Elevation: 1dp
Counter Display:

Position: X: 96dp, Y: 176dp (centered)
Text: "2"
Typography: H1 Display (48sp Bold), White
Text Align: Center
Counter Label:

Position: X: 96dp, Y: 216dp (centered)
Text: "Today"
Typography: Body Small (14sp Regular), White
Text Align: Center
Line Height: 20sp
Progress Circle:

Position: X: 220dp, Y: 164dp
Dimensions: 64dp × 64dp
Stroke Width: 6dp
Background: Golden Brown (#C49E69) at 30% opacity
Progress: Success Sage (#27AE60)
Progress Value: 66% (2 of 3 daily allowance)
Progress Text:

Position: X: 252dp, Y: 196dp (centered)
Text: "2/3"
Typography: Body Medium (16sp Medium), Charcoal (#333333)
Remaining Label:

Position: X: 176dp, Y: 232dp
Text: "1 roti remaining today"
Typography: Body Small (14sp Regular), Warm Gray (#8D6E63)
Text Alignment: Left
Max Width: 132dp
Daily Allowance Exhausted State (Alternative State)
Daily Allowance Card (Exhausted State):

Position: X: 16dp, Y: 124dp
Dimensions: 328dp × 144dp
Background: Warm Gray (#8D6E63) at 50% opacity
Corner Radius: 16dp, Elevation: 1dp
Padding: 20dp all sides
Counter Display (Exhausted State):

Text: "3"
Counter Label: "Used"
Exhausted State Message:

Text: "You've used all roti for today."
Typography: Body Small (14sp Regular), Warm Gray (#8D6E63)
Reset Time Information:

Text: "Resets at midnight"
Typography: Caption (12sp Regular), Warm Gray (#8D6E63)
Quick Actions Grid (16dp, 284dp → 344dp, 428dp)
Grid Layout:

Columns: 2, Rows: 2
Gap: 12dp horizontal, 12dp vertical
Button Size: 158dp × 66dp each
QR Token Button (Top Left):

Position: X: 16dp, Y: 284dp
Background: Burnt Orange (#E67E22)
Corner Radius: 12dp, Elevation: 2dp
Icon: qr_code, 24dp, White
Title: "QR Token"
Subtitle: "Show your daily code"
Typography: Body Medium (16sp Medium), White
Action: Navigate to QR display screen
Accessibility Label: "View my daily QR token"
Find Tandoor Button (Top Right):

Position: X: 186dp, Y: 284dp
Background: Surface Tan (#F5E1A4)
Corner Radius: 12dp, Elevation: 1dp
Icon: location_on, 24dp, Charcoal (#333333)
Text: "Find Tandoor"
Typography: Body Medium (16sp Medium), Charcoal (#333333)
View History Button (Bottom Left):

Position: X: 16dp, Y: 362dp
Background: Surface Tan (#F5E1A4)
Corner Radius: 12dp, Elevation: 1dp
Icon: history, 24dp, Charcoal (#333333)
Text: "History"
Typography: Body Medium (16sp Medium), Charcoal (#333333)
Emergency Button (Bottom Right):

Position: X: 186dp, Y: 362dp
Background: Error Coral (#E57373)
Corner Radius: 12dp, Elevation: 2dp
Icon: emergency, 24dp, White
Text: "Emergency"
Typography: Body Medium (16sp Medium), White
Accessibility Label: "Call emergency help center"
Recent Activity Section (16dp, 444dp → 344dp, 680dp)
Section Header:

Position: X: 16dp, Y: 444dp
Text: "Recent Activity"
Typography: H3 Headline (20sp Medium), Charcoal (#333333)
Activity List Container:

Position: X: 16dp, Y: 476dp
Dimensions: 328dp × 204dp
Background: Surface Tan (#F5E1A4)
Corner Radius: 12dp, Padding: 16dp all sides
Activity Items:

"Roti collected at Ali's Tandoor" - 2 hours ago
"QR code scanned successfully" - 5 hours ago
"Found nearby tandoor" - Yesterday
Floating QR Scanner Button (304dp, 696dp → 360dp, 752dp)
Container:

Position: X: 304dp, Y: 696dp
Dimensions: 56dp × 56dp
Background: Burnt Orange (#E67E22)
Corner Radius: 28dp (circular)
Elevation: 6dp
Icon: qr_code_scanner, 24dp, White
Accessibility Label: "Open scanner"
Conditional Visibility:

Show When: Daily allowance < 3/3
Hide When: Daily allowance = 3/3
Animation: 300ms fade in/out
Bottom Navigation (0dp, 720dp → 360dp, 800dp)
Navigation Items:

Dashboard (Active): home icon, Burnt Orange (#E67E22)
Map: map icon, Warm Gray (#8D6E63)
History: history icon, Warm Gray (#8D6E63)
Profile: person icon, Warm Gray (#8D6E63)
Screen 2: Mobile QR Code Scanner (English)
Canvas Size: 360dp × 800dp | Background: Black (#000000)

**📱 Mobile QR Code Scanner Interface – Complete Implementation Specification (English Version)**

**Canvas Configuration:**
- Size: 360dp × 800dp
- Background: Live camera feed integration (CameraX for Android / AVFoundation for iOS)
- Device Orientation: Portrait (locked)
- Language: English (LTR layout)
- Status Bar: Hidden for immersive experience

**🔳 1. Full-Screen Camera Viewport**
- Position: X: 0dp, Y: 0dp
- Dimensions: 360dp × 800dp
- Background: Real-time camera stream with live preview
- UI Overlay: Black (#000000) at 40% opacity for enhanced UI element visibility
- **Developer Implementation:** Use CameraX (Android) or AVFoundation (iOS) for camera integration. Ensure UI elements render above camera preview layer.

**🧭 2. Top Control Bar (Enhanced)**
- Container: X: 0dp, Y: 44dp → 360dp × 64dp
- Background: Black (#000000) at 70% opacity
- Visual Effects: 8dp backdrop blur for professional appearance
- Padding: 16dp horizontal margins

**a. Close Button (LTR Positioning)**
- Position: X: 16dp, Y: 60dp (left side for English)
- Dimensions: 40dp × 40dp circular button
- Background: Black (#000000) at 85% opacity
- Icon: Material Design 'close' icon (24dp, White)
- Corner Radius: 20dp (perfect circle)
- Touch Target: Minimum 48dp × 48dp for accessibility
- Accessibility: "Close QR scanner and return to dashboard"
- Interaction: Immediate return to previous screen

**b. Flash Toggle Button**
- Position: X: 304dp, Y: 60dp (right side for English)
- Dimensions: 40dp × 40dp circular button
- Background: Black (#000000) at 85% opacity
- Icons: flash_on / flash_off (24dp, White)
- Corner Radius: 20dp
- Touch Target: 48dp × 48dp
- Active State: Yellow (#FFD700) background when flash is ON
- Accessibility: "Toggle camera flashlight on/off"
- State Persistence: Remember flash state during session

**🟩 3. Professional Scanning Frame**
- Position: X: 60dp, Y: 280dp (centered horizontally)
- Dimensions: 240dp × 240dp square
- Background: Transparent with subtle grid overlay for alignment guidance

**a. Animated Scanning Border**
- Border Color: Burnt Orange (#E67E22) - matching app brand
- Border Width: 4dp solid stroke
- Corner Radius: 20dp for modern appearance
- Corner Brackets: 40dp length × 6dp width at each corner
- Glow Effect: 2dp outer glow with Burnt Orange color
- Animation: Pulsing opacity 0.7 → 1.0 every 1.2 seconds (infinite loop)

**b. Center Focus Indicator**
- Icon: Material Design 'center_focus_strong'
- Size: 32dp × 32dp
- Color: Burnt Orange (#E67E22)
- Position: Exact center (X: 180dp, Y: 400dp)
- Animation: Gentle scale transform 0.85 → 1.15 (2.5s duration, infinite)
- Additional Effect: Subtle expanding concentric circles

**c. Scanning Line Animation**
- Dimensions: 240dp width × 3dp height
- Color: Burnt Orange (#E67E22) with gradient fade effect
- Animation: Vertical sweep from top to bottom (2.5s duration, infinite loop)
- Visual Effect: Trailing glow with opacity fade for professional appearance

**📝 4. Bottom Instruction Panel (Enhanced)**
- Container: X: 0dp, Y: 600dp → 360dp × 200dp
- Background: Background Cream (#FAF3E0) at 95% opacity
- Corner Radius: 24dp (top corners only for modern sheet design)
- Padding: 24dp on all sides
- Visual Effects: 12dp backdrop blur for depth

**a. Primary Instruction Text**
- Content: "Position QR code within the frame"
- Typography: Body Large (18sp Regular weight)
- Color: Charcoal (#333333)
- Alignment: Center-aligned
- Constraints: Max width 312dp for optimal readability
- Line Height: 26sp for comfortable reading
- Position: X: 24dp, Y: 640dp

**b. Manual Entry Button**
- Position: X: 80dp, Y: 700dp (centered horizontally)
- Dimensions: 200dp × 48dp
- Background: Surface Tan (#F5E1A4) - matching app secondary color
- Text: "Enter code manually"
- Typography: Body Medium (16sp Medium weight)
- Text Color: Charcoal (#333333)
- Corner Radius: 24dp for pill-shaped appearance
- Elevation: 2dp shadow for tactile feedback
- Accessibility: "Manually enter QR code instead of scanning"
- Action: Navigate to manual code entry screen

**✅ 5. Scanner States & User Feedback System**

**🟩 Default Scanning State**
- Border Color: Burnt Orange (#E67E22)
- Instruction: "Position QR code within the frame"
- Status: All scanning animations active
- Audio: Optional subtle scanning sound effect

**✔️ QR Code Detected State**
- Border Color: Success Sage (#27AE60)
- Icon: check_circle (Success Sage color, 32dp)
- Instruction: "QR code detected, processing..."
- Haptic Feedback: Light impact vibration
- Duration: Brief transition state (0.5-1s)

**🎉 Success State**
- Border Color: Success Sage (#27AE60)
- Background Overlay: Success Sage (#27AE60) at 15% opacity
- Instruction: "Success! Roti allowance updated"
- Haptic Feedback: Success notification pattern
- Auto-transition: 2-second delay before returning to dashboard
- Visual Effect: Brief success animation (checkmark or similar)

**❌ Error States with Specific Messaging**

**Invalid QR Code:**
- Border Color: Error Coral (#E57373)
- Message: "Invalid QR code. Please try again"
- Duration: 3 seconds before returning to scanning
- Haptic: Error vibration pattern

**Network Connection Error:**
- Border Color: Golden Brown (#C49E69)
- Message: "No connection. Saved for later sync"
- Behavior: Cache scan locally for later processing
- Icon: wifi_off indicator

**Daily Limit Reached:**
- Border Color: Error Coral (#E57373)
- Message: "Daily limit reached. Try again tomorrow"
- Action: Disable scanning, show return to dashboard option
- Additional Info: Display reset time if available

**🔧 Technical Implementation Notes:**
- Ensure camera permissions are requested before screen load
- Implement proper camera lifecycle management (pause/resume)
- Handle device rotation gracefully (lock to portrait recommended)
- Optimize for various screen densities and sizes
- Include proper error handling for camera initialization failures
- Implement accessibility features for visually impaired users
- Test scanning performance under various lighting conditions

**LABORER DASHBOARD & QR SCANNING (URDU VERSION)**
**Screen 1: Laborer Dashboard (اردو ورژن)**
Canvas Size: 360dp × 800dp | Background: Background Cream (#FAF3E0)

Top App Bar (0dp, 44dp → 360dp, 108dp)
Container:

Position: X: 0dp, Y: 44dp
Dimensions: 360dp × 64dp
Background: Burnt Orange (#E67E22)
Elevation: 4dp with enhanced shadow
Greeting Section (اردو):

Position: X: 328dp, Y: 60dp (RTL)
Text: "السلام علیکم، احمد"
Typography: H3 Headline (24sp Bold), White
Line Height: 36sp
Text Alignment: Right (RTL)
Profile Icon:

Position: X: 280dp, Y: 68dp
Dimensions: 32dp × 32dp
Icon: person, 24dp, White
Touch Target: 48dp × 48dp
Action: Navigate to "My Profile" screen
Accessibility Label: "پروفائل کھولیں"
Notification Icon:

Position: X: 312dp, Y: 68dp
Dimensions: 32dp × 32dp
Icon: notifications, 24dp, White
Touch Target: 48dp × 48dp
Badge: 8dp circle, Error Coral (#E57373) if notifications present
Accessibility Label: "اطلاعات دیکھیں"
Daily Allowance Card (16dp, 124dp → 344dp, 268dp)
Container:

Position: X: 16dp, Y: 124dp
Dimensions: 328dp × 144dp
Background: Surface Tan (#F5E1A4)
Corner Radius: 16dp, Elevation: 2dp
Padding: 20dp all sides
Allowance Counter:

Position: X: 36dp, Y: 144dp
Dimensions: 120dp × 104dp
Background: Burnt Orange (#E67E22)
Corner Radius: 12dp, Elevation: 1dp
Counter Display:

Position: X: 96dp, Y: 176dp (centered)
Text: "2"
Typography: H1 Display (48sp Bold), White
Text Align: Center
Counter Label (اردو):

Position: X: 96dp, Y: 216dp (centered)
Text: "آج"
Typography: Body Small (16sp Regular), White
Text Align: Center
Line Height: 24sp
Progress Circle:

Position: X: 220dp, Y: 164dp
Dimensions: 64dp × 64dp
Stroke Width: 6dp
Background: Golden Brown (#C49E69) at 30% opacity
Progress: Success Sage (#27AE60)
Progress Value: 66% (2 of 3 daily allowance)
Progress Direction: Right-to-left (RTL)
Progress Text:

Position: X: 252dp, Y: 196dp (centered)
Text: "2/3"
Typography: Body Medium (16sp Medium), Charcoal (#333333)
Remaining Label (اردو):

Position: X: 152dp, Y: 232dp (RTL)
Text: "آج 1 روٹی باقی ہے"
Typography: Body Small (16sp Regular), Warm Gray (#8D6E63)
Text Alignment: Right (RTL)
Max Width: 172dp
Daily Allowance Exhausted State (متبادل حالت)
Daily Allowance Card (Exhausted State):

Position: X: 16dp, Y: 124dp
Dimensions: 328dp × 144dp
Background: Warm Gray (#8D6E63) at 50% opacity
Corner Radius: 16dp, Elevation: 1dp
Padding: 20dp all sides
Counter Display (Exhausted State):

Text: "3"
Counter Label: "استعمال شدہ"
Exhausted State Message (اردو):

Text: "آپ نے آج کی تمام روٹی استعمال کر لی ہے۔"
Typography: Body Small (16sp Regular), Warm Gray (#8D6E63)
Reset Time Information (اردو):

Text: "آدھی رات کو ری سیٹ ہوگا"
Typography: Caption (14sp Regular), Warm Gray (#8D6E63)
Quick Actions Grid (16dp, 284dp → 344dp, 428dp)
Grid Layout:

Columns: 2, Rows: 2
Gap: 12dp horizontal, 12dp vertical
Button Size: 158dp × 66dp each
Button Order: Right-to-left (RTL)
My QR Token Button (Top Right - اردو):

Position: X: 186dp, Y: 284dp (RTL)
Background: Burnt Orange (#E67E22)
Corner Radius: 12dp, Elevation: 2dp
Icon: qr_code, 24dp, White
Title: "میرا QR ٹوکن"
Subtitle: "اپنا یومیہ کوڈ دکھائیں"
Typography: Body Medium (18sp Medium), White
Action: Navigate to QR display screen
Accessibility Label: "میرا یومیہ QR ٹوکن دیکھیں"
Find Tandoor Button (Top Left - اردو):

Position: X: 16dp, Y: 284dp (RTL)
Background: Surface Tan (#F5E1A4)
Corner Radius: 12dp, Elevation: 1dp
Icon: location_on, 24dp, Charcoal (#333333)
Text: "تندور تلاش کریں"
Typography: Body Medium (18sp Medium), Charcoal (#333333)
View History Button (Bottom Right - اردو):

Position: X: 186dp, Y: 362dp (RTL)
Background: Surface Tan (#F5E1A4)
Corner Radius: 12dp, Elevation: 1dp
Icon: history, 24dp, Charcoal (#333333)
Text: "تاریخ"
Typography: Body Medium (18sp Medium), Charcoal (#333333)
Emergency Button (Bottom Left - اردو):

Position: X: 16dp, Y: 362dp (RTL)
Background: Error Coral (#E57373)
Corner Radius: 12dp, Elevation: 2dp
Icon: emergency, 24dp, White
Text: "ایمرجنسی"
Typography: Body Medium (18sp Medium), White
Accessibility Label: "ایمرجنسی ہیلپ سینٹر کو کال کریں"
Recent Activity Section (16dp, 444dp → 344dp, 680dp)
Section Header (اردو):

Position: X: 328dp, Y: 444dp (RTL)
Text: "حالیہ سرگرمی"
Typography: H3 Headline (22sp Bold), Charcoal (#333333)
Text Alignment: Right
Activity List Container:

Position: X: 16dp, Y: 476dp
Dimensions: 328dp × 204dp
Background: Surface Tan (#F5E1A4)
Corner Radius: 12dp, Padding: 16dp all sides
Activity Items (اردو):

"علی کے تندور سے روٹی لی گئی" - 2 گھنٹے پہلے
"QR کوڈ کامیابی سے اسکین ہوا" - 5 گھنٹے پہلے
"قریبی تندور ملا" - کل
Floating QR Scanner Button (304dp, 696dp → 360dp, 752dp)
Container:

Position: X: 304dp, Y: 696dp
Dimensions: 56dp × 56dp
Background: Burnt Orange (#E67E22)
Corner Radius: 28dp (circular)
Elevation: 6dp
Icon: qr_code_scanner, 24dp, White
Accessibility Label: "اسکینر کھولیں"
Conditional Visibility:

Show When: Daily allowance < 3/3
Hide When: Daily allowance = 3/3
Animation: 300ms fade in/out
Bottom Navigation (0dp, 720dp → 360dp, 800dp)
Navigation Items (اردو):

Dashboard (Active): home icon, "ڈیش بورڈ"
Map: map icon, "نقشہ"
History: history icon, "تاریخ"
Profile: person icon, "پروفائل"
Screen 2: Mobile QR Code Scanner (اردو ورژن)
Canvas Size: 360dp × 800dp | Background: Black (#000000)


## urdu mobile scanner:
 📱 موبائل QR کوڈ اسکینر انٹرفیس – مکمل نافذکاری کی تفصیلات (اردو ورژن)

کینوس کنفیگریشن:

سائز: 360dp × 800dp
پس منظر: لائیو کیمرہ فیڈ انٹیگریشن (Android کے لیے CameraX / iOS کے لیے AVFoundation)
ڈیوائس کی سمت: پورٹریٹ (لاک شدہ)
زبان: اردو (RTL لے آؤٹ)
اسٹیٹس بار: غامر تجربے کے لیے چھپا ہوا
🔳 1. مکمل اسکرین کیمرہ ویو پورٹ

پوزیشن: X: 0dp, Y: 0dp
ابعاد: 360dp × 800dp
پس منظر: لائیو پیش منظری کے ساتھ ریئل ٹائم کیمرہ سٹریم
UI اوور لے: UI عناصر کی بہتر مرئیت کے لیے Black (#000000) 40% شفافیت پر
ڈیولپر نافذکاری: کیمرہ انٹیگریشن کے لیے CameraX (Android) یا AVFoundation (iOS) استعمال کریں۔ یقینی بنائیں کہ UI عناصر کیمرہ پیش منظری کی تہ کے اوپر رینڈر ہوں۔
🧭 2. اوپری کنٹرول بار (بہتر شدہ)

کنٹینر: X: 0dp, Y: 44dp → 360dp × 64dp
پس منظر: Black (#000000) 70% شفافیت پر
بصری اثرات: پیشہ ورانہ ظاہری شکل کے لیے 8dp بیک ڈراپ بلر
پیڈنگ: 16dp افقی حاشیے
a. بند کرنے کا بٹن (RTL پوزیشننگ)

پوزیشن: X: 304dp, Y: 60dp (اردو کے لیے دائیں طرف)
ابعاد: 40dp × 40dp دائرہ نما بٹن
پس منظر: Black (#000000) 85% شفافیت پر
آئیکن: Material Design 'arrow_forward' آئیکن (24dp, سفید) - RTL کے لیے
کونے کا ریڈیس: 20dp (کامل دائرہ)
ٹچ ٹارگٹ: رسائی کے لیے کم سے کم 48dp × 48dp
رسائی: "QR اسکینر بند کریں اور ڈیش بورڈ پر واپس جائیں"
تعامل: پچھلی اسکرین پر فوری واپسی
b. فلیش ٹوگل بٹن

پوزیشن: X: 16dp, Y: 60dp (اردو کے لیے بائیں طرف)
ابعاد: 40dp × 40dp دائرہ نما بٹن
پس منظر: Black (#000000) 85% شفافیت پر
آئیکنز: flash_on / flash_off (24dp, سفید)
کونے کا ریڈیس: 20dp
ٹچ ٹارگٹ: 48dp × 48dp
فعال حالت: فلیش آن ہونے پر Yellow (#FFD700) پس منظر
رسائی: "کیمرہ فلیش لائٹ آن/آف کریں"
حالت کی برقراری: سیشن کے دوران فلیش کی حالت یاد رکھیں
🟩 3. پیشہ ورانہ اسکیننگ فریم

پوزیشن: X: 60dp, Y: 280dp (افقی طور پر مرکز میں)
ابعاد: 240dp × 240dp مربع
پس منظر: سیدھ کی رہنمائی کے لیے لطیف گرڈ اوور لے کے ساتھ شفاف
a. متحرک اسکیننگ بارڈر

بارڈر کا رنگ: Burnt Orange (#E67E22) - ایپ برانڈ سے میچ کرتا ہوا
بارڈر کی چوڑائی: 4dp ٹھوس اسٹروک
کونے کا ریڈیس: جدید ظاہری شکل کے لیے 20dp
کونے کے بریکٹس: ہر کونے پر 40dp لمبائی × 6dp چوڑائی
چمک کا اثر: Burnt Orange رنگ کے ساتھ 2dp بیرونی چمک
حرکت: ہر 1.2 سیکنڈ میں شفافیت 0.7 → 1.0 (لامحدود لوپ)
b. مرکزی فوکس انڈیکیٹر

آئیکن: Material Design 'center_focus_strong'
سائز: 32dp × 32dp
رنگ: Burnt Orange (#E67E22)
پوزیشن: بالکل مرکز (X: 180dp, Y: 400dp)
حرکت: نرم اسکیل ٹرانسفارم 0.85 → 1.15 (2.5s مدت، لامحدود)
اضافی اثر: لطیف پھیلتے ہوئے مرکزی دائرے
c. اسکیننگ لائن حرکت

ابعاد: 240dp چوڑائی × 3dp اونچائی
رنگ: گریڈینٹ فیڈ اثر کے ساتھ Burnt Orange (#E67E22)
حرکت: اوپر سے نیچے عمودی جھاڑو (2.5s مدت، لامحدود لوپ)
بصری اثر: پیشہ ورانہ ظاہری شکل کے لیے شفافیت فیڈ کے ساتھ پیچھے چمک
📝 4. نیچے کی ہدایات پینل (بہتر شدہ)

کنٹینر: X: 0dp, Y: 600dp → 360dp × 200dp
پس منظر: Background Cream (#FAF3E0) 95% شفافیت پر
کونے کا ریڈیس: جدید شیٹ ڈیزائن کے لیے 24dp (صرف اوپری کونے)
پیڈنگ: تمام اطراف 24dp
بصری اثرات: گہرائی کے لیے 12dp بیک ڈراپ بلر
a. بنیادی ہدایات کا متن

مواد: "QR کوڈ کو فریم کے اندر رکھیں"
ٹائپوگرافی: Body Large (20sp Regular وزن) - اردو کے لیے بڑا سائز
رنگ: Charcoal (#333333)
سیدھ: مرکز میں سیدھ
پابندیاں: بہترین پڑھنے کے لیے زیادہ سے زیادہ چوڑائی 312dp
لائن کی اونچائی: آرام دہ پڑھنے کے لیے 30sp
پوزیشن: X: 24dp, Y: 640dp
b. دستی اندراج بٹن

پوزیشن: X: 80dp, Y: 700dp (افقی طور پر مرکز میں)
ابعاد: 200dp × 48dp
پس منظر: Surface Tan (#F5E1A4) - ایپ کے ثانوی رنگ سے میچ کرتا ہوا
متن: "کوڈ دستی طور پر داخل کریں"
ٹائپوگرافی: Body Medium (18sp Medium وزن) - اردو کے لیے
متن کا رنگ: Charcoal (#333333)
کونے کا ریڈیس: گولی کی شکل کے لیے 24dp
بلندی: ٹچ فیڈ بیک کے لیے 2dp سایہ
رسائی: "اسکین کرنے کے بجائے QR کوڈ دستی طور پر داخل کریں"
عمل: دستی کوڈ اندراج اسکرین پر جائیں
✅ 5. اسکینر کی حالات اور صارف فیڈ بیک سسٹم

🟩 ڈیفالٹ اسکیننگ حالت

بارڈر کا رنگ: Burnt Orange (#E67E22)
ہدایت: "QR کوڈ کو فریم کے اندر رکھیں"
حالت: تمام اسکیننگ حرکات فعال
آڈیو: اختیاری لطیف اسکیننگ ساؤنڈ ایفیکٹ
✔️ QR کوڈ کا پتہ چلنے کی حالت

بارڈر کا رنگ: Success Sage (#27AE60)
آئیکن: check_circle (Success Sage رنگ، 32dp)
ہدایت: "QR کوڈ ملا، پروسیسنگ..."
ہپٹک فیڈ بیک: ہلکا اثر کمپن
مدت: مختصر منتقلی حالت (0.5-1s)
🎉 کامیابی کی حالت

بارڈر کا رنگ: Success Sage (#27AE60)
پس منظر اوور لے: Success Sage (#27AE60) 15% شفافیت پر
ہدایت: "کامیابی! روٹی الاؤنس اپڈیٹ ہو گیا"
ہپٹک فیڈ بیک: کامیابی کی اطلاع کا پیٹرن
خودکار منتقلی: ڈیش بورڈ پر واپس جانے سے پہلے 2 سیکنڈ کی تاخیر
بصری اثر: مختصر کامیابی کی حرکت (چیک مارک یا اسی طرح)
❌ مخصوص پیغام رسانی کے ساتھ خرابی کی حالات

غلط QR کوڈ:

بارڈر کا رنگ: Error Coral (#E57373)
پیغام: "غلط QR کوڈ۔ دوبارہ کوشش کریں"
مدت: اسکیننگ پر واپس جانے سے پہلے 3 سیکنڈ
ہپٹک: خرابی کمپن پیٹرن
نیٹ ورک کنکشن کی خرابی:

بارڈر کا رنگ: Golden Brown (#C49E69)
پیغام: "کوئی کنکشن نہیں۔ بعد میں سنک کے لیے محفوظ"
رفتار: بعد میں پروسیسنگ کے لیے اسکین کو مقامی طور پر کیش کریں
آئیکن: wifi_off انڈیکیٹر
یومیہ حد ختم:

بارڈر کا رنگ: Error Coral (#E57373)
پیغام: "یومیہ حد ختم۔ کل دوبارہ کوشش کریں"
عمل: اسکیننگ غیر فعال کریں، ڈیش بورڈ پر واپسی کا آپشن دکھائیں
اضافی معلومات: اگر دستیاب ہو تو ری سیٹ ٹائم دکھائیں
🔧 تکنیکی نافذکاری کے نوٹس:

اسکرین لوڈ سے پہلے کیمرہ کی اجازات کی درخواست کریں
مناسب کیمرہ لائف سائیکل منیجمنٹ (pause/resume) نافذ کریں
ڈیوائس کی گردش کو خوبصورتی سے سنبھالیں (پورٹریٹ میں لاک کرنا تجویز کردہ)
مختلف اسکرین کثافت اور سائز کے لیے بہتر بنائیں
کیمرہ ابتدائی ناکامیوں کے لیے مناسب خرابی ہینڈلنگ شامل کریں
بصری طور پر معذور صارفین کے لیے رسائی کی خصوصیات نافذ کریں
مختلف روشنی کی حالات میں اسکیننگ کی کارکردگی کا ٹیسٹ کریں


---
prompt4:
**TANDOOR OWNER DASHBOARD (ENGLISH VERSION)**

**Screen 1: Tandoor Owner Dashboard (English)**
Canvas Size: 360dp × 800dp | Background: Background Cream (#FAF3E0)

**Business Header Section (0dp, 44dp → 360dp, 164dp)**

Container:
- Position: X: 0dp, Y: 44dp
- Dimensions: 360dp × 120dp
- Background: Linear gradient from Burnt Orange (#E67E22) to Golden Brown (#C49E69)
- Elevation: 4dp with enhanced shadow

Business Identity:
- Position: X: 16dp, Y: 60dp (LTR positioning)
- Text: "Ali's Traditional Tandoor"
- Typography: H2 Headline (28sp Bold), White
- Line Height: 40sp, Max Width: 312dp, Text Align: Left

Verification Badge:
- Position: X: 16dp, Y: 92dp (LTR positioning)
- Icon: verified, 16dp, Success Sage (#27AE60)
- Text: "Verified Partner"
- Typography: Body Small (16sp Regular), White with 90% opacity, Left align

Status Indicator:
- Position: X: 16dp, Y: 112dp (LTR positioning)
- Status Dot: 12dp circle, Success Sage (#27AE60) with pulsing animation
- Text: "Open Now • Closes at 10:00 PM"
- Typography: Body Small (16sp Regular), White with 80% opacity, Left align

**QR Management Hub (244dp, 52dp → 328dp, 152dp)**

Container:
- Position: X: 244dp, Y: 52dp (LTR positioning)
- Dimensions: 84dp × 100dp
- Background: White, Corner Radius: 16dp, Elevation: 2dp, Padding: 12dp all sides

QR Code Display:
- Position: X: 256dp, Y: 64dp (LTR positioning)
- Dimensions: 60dp × 60dp
- Background: White, QR Code: Burnt Orange (#E67E22) foreground, Corner Radius: 8dp

Status Ring:
- Position: X: 250dp, Y: 58dp (LTR positioning)
- Dimensions: 72dp × 72dp
- Stroke: 3dp, Success Sage (#27AE60), Animation: Gentle rotation for active state

Quick Actions:
- Refresh Icon: refresh, 16dp, Warm Gray (#8D6E63)
- Position: X: 324dp, Y: 132dp (LTR positioning), Touch Target: 32dp × 32dp

Last Update:
- Position: X: 316dp, Y: 132dp (LTR positioning)
- Text: "2 min ago"
- Typography: Caption (14sp Regular), Warm Gray (#8D6E63), Left align

**Business Metrics Grid (16dp, 180dp → 344dp, 500dp)**

Grid Layout:
- Layout: 2×2 grid with 12dp gaps
- Card Dimensions: 158dp × 154dp each
- LTR Adaptation: Standard grid order (top-left, top-right, bottom-left, bottom-right)

**Revenue Card (16dp, 180dp → 174dp, 334dp)**

Container:
- Position: X: 16dp, Y: 180dp (LTR positioning)
- Background: Surface Tan (#F5E1A4)
- Corner Radius: 16dp, Elevation: 2dp, Padding: 16dp all sides

Icon:
- Position: X: 32dp, Y: 196dp (LTR positioning)
- Dimensions: 36dp × 36dp
- Background: Success Sage (#27AE60) circle, Icon: trending_up, 24dp, White

Primary Value:
- Position: X: 32dp, Y: 240dp (LTR positioning)
- Text: "PKR 12,450"
- Typography: H1 Headline (32sp Bold), Charcoal (#333333), Left align

Label:
- Position: X: 32dp, Y: 276dp (LTR positioning)
- Text: "Today's Revenue"
- Typography: Body Medium (18sp Regular), Warm Gray (#8D6E63), Left align

Trend Indicator:
- Position: X: 32dp, Y: 296dp (LTR positioning)
- Text: "+18% from yesterday"
- Typography: Caption (14sp Regular), Success Sage (#27AE60)
- Icon: arrow_upward, 12dp, Success Sage (#27AE60), positioned right of text

Progress Bar:
- Position: X: 32dp, Y: 314dp (LTR positioning)
- Dimensions: 126dp × 4dp
- Background: Golden Brown (#C49E69) at 30% opacity, Progress: Success Sage (#27AE60)
- Progress Value: 78% of daily target, Direction: Left to right

**Transaction Volume Card (186dp, 180dp → 344dp, 334dp)**

Container:
- Position: X: 186dp, Y: 180dp (LTR positioning)
- Same styling as Revenue card

Icon:
- Background: Burnt Orange (#E67E22) circle, Icon: receipt, 24dp, White

Primary Value:
- Text: "247"
- Typography: H1 Headline (32sp Bold), Charcoal (#333333)

Label:
- Text: "Roti Sold Today"
- Typography: Body Medium (18sp Regular), Warm Gray (#8D6E63)

Detail:
- Text: "Subsidized: 148 | Regular: 99"
- Typography: Caption (14sp Regular), Warm Gray (#8D6E63)

Mini Chart:
- Dimensions: 126dp × 8dp
- Subsidized bar: 60% width Success Sage (#27AE60)
- Regular bar: 40% width Golden Brown (#C49E69)

**Customer Rating Card (16dp, 346dp → 174dp, 500dp)**

Container:
- Position: X: 16dp, Y: 346dp (LTR positioning)
- Same styling as Revenue card

Icon:
- Background: Golden Brown (#C49E69) circle, Icon: star, 24dp, White

Primary Value:
- Text: "4.8"
- Typography: H1 Headline (32sp Bold), Charcoal (#333333)

Star Rating:
- 5 stars, each 16dp, 4dp spacing
- Filled: Golden Brown (#C49E69), Star order: Left to right

Label:
- Text: "Average Rating"
- Typography: Body Medium (18sp Regular), Warm Gray (#8D6E63)

Review Count:
- Text: "Based on 156 reviews"
- Typography: Caption (14sp Regular), Warm Gray (#8D6E63)

**Business Intelligence Card (186dp, 346dp → 344dp, 500dp)**

Container:
- Position: X: 186dp, Y: 346dp (LTR positioning)
- Same styling as Revenue card

Icon:
- Background: Charcoal (#333333) circle, Icon: insights, 24dp, White

Primary Value:
- Text: "92%"
- Typography: H1 Headline (32sp Bold), Charcoal (#333333)

Label:
- Text: "Customer Return Rate"
- Typography: Body Medium (18sp Regular), Warm Gray (#8D6E63)

Insight:
- Text: "Peak: 12-2 PM, 6-8 PM"
- Typography: Caption (14sp Regular), Warm Gray (#8D6E63)

AI Recommendation:
- Text: "Consider lunch specials"
- Typography: Caption (14sp Regular), Success Sage (#27AE60)

**Quick Actions Panel (16dp, 516dp → 344dp, 596dp)**

Container:
- Position: X: 16dp, Y: 516dp
- Dimensions: 328dp × 80dp
- Background: White, Corner Radius: 16dp, Elevation: 1dp
- Padding: 16dp horizontal, 12dp vertical

Action Buttons:
- Height: 56dp, Width: 100dp each (English)
- Spacing: 12dp, Corner Radius: 12dp
- Scroll Direction: Left to right

New QR Button:
- Background: Burnt Orange (#E67E22), Icon: qr_code, 20dp, White
- Text: "New QR", Typography: Caption (14sp Medium), White

Analytics Button:
- Background: Surface Tan (#F5E1A4), Icon: analytics, 20dp, Charcoal (#333333)
- Text: "Reports", Typography: Caption (14sp Medium), Charcoal (#333333)

Reviews Button:
- Background: Surface Tan (#F5E1A4), Icon: star, 20dp, Charcoal (#333333)
- Text: "Reviews", Typography: Caption (14sp Medium), Charcoal (#333333)

**Live Activity Feed (16dp, 612dp → 344dp, 720dp)**

Container:
- Position: X: 16dp, Y: 612dp
- Dimensions: 328dp × 108dp
- Background: White, Corner Radius: 16dp, Elevation: 1dp
- Padding: 16dp all sides

Section Header:
- Position: X: 32dp, Y: 628dp (LTR positioning)
- Text: "Live Activity"
- Typography: H3 Headline (22sp Bold), Charcoal (#333333), Left align

Auto-refresh Indicator:
- Position: X: 312dp, Y: 632dp (LTR positioning)
- Icon: refresh, 16dp, Success Sage (#27AE60)
- Animation: Gentle rotation every 30 seconds

Activity Item:
- Position: X: 32dp, Y: 652dp (LTR positioning)
- Height: 48dp

Customer Avatar:
- Position: X: 32dp, Y: 652dp (LTR positioning)
- Dimensions: 32dp × 32dp
- Background: Golden Brown (#C49E69) circle, Icon: person, 20dp, White
- Corner Radius: 16dp

Activity Text:
- Position: X: 72dp, Y: 656dp (LTR positioning)
- Text: "Ahmad Khan purchased 2 roti (PKR 60)"
- Typography: Body Medium (16sp Regular), Charcoal (#333333)
- Max Width: 312dp, Left align

Timestamp:
- Position: X: 72dp, Y: 676dp (LTR positioning)
- Text: "2 minutes ago"
- Typography: Caption (14sp Regular), Warm Gray (#8D6E63), Left align

Transaction Badge:
- Position: X: 312dp, Y: 660dp (LTR positioning)
- Background: Success Sage (#27AE60)
- Text: "PAID", Typography: Caption (12sp Bold), White
- Padding: 4dp horizontal, 2dp vertical, Corner Radius: 4dp

**Bottom Navigation (0dp, 720dp → 360dp, 800dp)**

Navigation Items:
- Dashboard (Active): Icon: dashboard, Burnt Orange (#E67E22), Text: "Dashboard"
- Analytics: Icon: analytics, Warm Gray (#8D6E63), Text: "Analytics"
- Orders: Icon: receipt_long, Warm Gray (#8D6E63), Text: "Orders"
- Settings: Icon: settings, Warm Gray (#8D6E63), Text: "Settings"

---

**TANDOOR OWNER DASHBOARD (URDU VERSION)**

**Screen 1: تندور مالک ڈیش بورڈ (اردو)**
Canvas Size: 360dp × 800dp | Background: Background Cream (#FAF3E0)

**کاروباری ہیڈر سیکشن (0dp, 44dp → 360dp, 164dp)**

Container:
- Position: X: 0dp, Y: 44dp
- Dimensions: 360dp × 120dp
- Background: Linear gradient from Burnt Orange (#E67E22) to Golden Brown (#C49E69)
- Elevation: 4dp with enhanced shadow

کاروباری شناخت:
- Position: X: 344dp, Y: 60dp (RTL positioning - right-aligned)
- Text: "علی کا روایتی تندور"
- Typography: H2 Headline (28sp Bold), White
- Line Height: 40sp, Max Width: 312dp, Text Align: Right

تصدیقی بیج:
- Position: X: 344dp, Y: 92dp (RTL positioning - right-aligned)
- Icon: verified, 16dp, Success Sage (#27AE60)
- Text: "تصدیق شدہ پارٹنر"
- Typography: Body Small (16sp Regular), White with 90% opacity, Right align

اسٹیٹس انڈیکیٹر:
- Position: X: 344dp, Y: 112dp (RTL positioning - right-aligned)
- Status Dot: 12dp circle, Success Sage (#27AE60) with pulsing animation
- Text: "اب کھلا ہے • رات 10:00 بجے بند"
- Typography: Body Small (16sp Regular), White with 80% opacity, Right align

**QR منیجمنٹ ہب (16dp, 52dp → 100dp, 152dp)**

Container:
- Position: X: 16dp, Y: 52dp (RTL positioning)
- Dimensions: 84dp × 100dp
- Background: White, Corner Radius: 16dp, Elevation: 2dp, Padding: 12dp all sides

QR کوڈ ڈسپلے:
- Position: X: 28dp, Y: 64dp (RTL positioning)
- Dimensions: 60dp × 60dp
- Background: White, QR Code: Burnt Orange (#E67E22) foreground, Corner Radius: 8dp

اسٹیٹس رنگ:
- Position: X: 22dp, Y: 58dp (RTL positioning)
- Dimensions: 72dp × 72dp
- Stroke: 3dp, Success Sage (#27AE60), Animation: Gentle rotation for active state

فوری اعمال:
- دوبارہ بنانے کا آئیکن: refresh, 16dp, Warm Gray (#8D6E63)
- Position: X: 20dp, Y: 132dp (RTL positioning), Touch Target: 32dp × 32dp

آخری اپڈیٹ:
- Position: X: 28dp, Y: 132dp (RTL positioning)
- Text: "2 منٹ پہلے"
- Typography: Caption (14sp Regular), Warm Gray (#8D6E63), Right align

**کاروباری میٹرکس گرڈ (16dp, 180dp → 344dp, 500dp)**

Grid Layout:
- Layout: 2×2 grid with 12dp gaps
- Card Dimensions: 158dp × 154dp each
- RTL Adaptation: Grid order reverses (top-right becomes top-left)

**آمدنی کارڈ (186dp, 180dp → 344dp, 334dp)**

Container:
- Position: X: 186dp, Y: 180dp (RTL positioning)
- Background: Surface Tan (#F5E1A4)
- Corner Radius: 16dp, Elevation: 2dp, Padding: 16dp all sides

Icon:
- Position: X: 310dp, Y: 196dp (RTL positioning)
- Dimensions: 36dp × 36dp
- Background: Success Sage (#27AE60) circle, Icon: trending_up, 24dp, White

Primary Value:
- Position: X: 310dp, Y: 240dp (RTL positioning)
- Text: "PKR 12,450"
- Typography: H1 Headline (32sp Bold), Charcoal (#333333), Right align

Label:
- Position: X: 310dp, Y: 276dp (RTL positioning)
- Text: "آج کی آمدنی"
- Typography: Body Medium (18sp Regular), Warm Gray (#8D6E63), Right align

Trend Indicator:
- Position: X: 310dp, Y: 296dp (RTL positioning)
- Text: "کل سے +18%"
- Typography: Caption (14sp Regular), Success Sage (#27AE60)
- Icon: arrow_upward, 12dp, Success Sage (#27AE60), positioned left of text (RTL)

Progress Bar:
- Position: X: 310dp, Y: 314dp (RTL positioning)
- Dimensions: 126dp × 4dp
- Background: Golden Brown (#C49E69) at 30% opacity, Progress: Success Sage (#27AE60)
- Progress Value: 78% of daily target, Direction: Right to left

**ٹرانزیکشن والیوم کارڈ (16dp, 180dp → 174dp, 334dp)**

Container:
- Position: X: 16dp, Y: 180dp (RTL positioning)
- Same styling as Revenue card

Icon:
- Background: Burnt Orange (#E67E22) circle, Icon: receipt, 24dp, White

Primary Value:
- Text: "247"
- Typography: H1 Headline (32sp Bold), Charcoal (#333333)

Label:
- Text: "آج فروخت شدہ روٹی"
- Typography: Body Medium (18sp Regular), Warm Gray (#8D6E63)

Detail:
- Text: "سبسڈی: 148 | عام: 99"
- Typography: Caption (14sp Regular), Warm Gray (#8D6E63)

Mini Chart:
- Dimensions: 126dp × 8dp
- Subsidized bar: 60% width Success Sage (#27AE60)
- Regular bar: 40% width Golden Brown (#C49E69)

**کسٹمر ریٹنگ کارڈ (186dp, 346dp → 344dp, 500dp)**

Container:
- Position: X: 186dp, Y: 346dp (RTL positioning)
- Same styling as Revenue card

Icon:
- Background: Golden Brown (#C49E69) circle, Icon: star, 24dp, White

Primary Value:
- Text: "4.8"
- Typography: H1 Headline (32sp Bold), Charcoal (#333333)

Star Rating:
- 5 stars, each 16dp, 4dp spacing
- Filled: Golden Brown (#C49E69), Star order: Right to left

Label:
- Text: "اوسط ریٹنگ"
- Typography: Body Medium (18sp Regular), Warm Gray (#8D6E63)

Review Count:
- Text: "156 جائزوں کی بنیاد پر"
- Typography: Caption (14sp Regular), Warm Gray (#8D6E63)

**کاروباری انٹیلیجنس کارڈ (16dp, 346dp → 174dp, 500dp)**

Container:
- Position: X: 16dp, Y: 346dp (RTL positioning)
- Same styling as Revenue card

Icon:
- Background: Charcoal (#333333) circle, Icon: insights, 24dp, White

Primary Value:
- Text: "92%"
- Typography: H1 Headline (32sp Bold), Charcoal (#333333)

Label:
- Text: "کسٹمر واپسی کی شرح"
- Typography: Body Medium (18sp Regular), Warm Gray (#8D6E63)

Insight:
- Text: "چوٹی: دوپہر 12-2، شام 6-8"
- Typography: Caption (14sp Regular), Warm Gray (#8D6E63)

AI Recommendation:
- Text: "دوپہر کے خصوصی آفرز پر غور کریں"
- Typography: Caption (14sp Regular), Success Sage (#27AE60)

**فوری اعمال پینل (16dp, 516dp → 344dp, 596dp)**

Container:
- Position: X: 16dp, Y: 516dp
- Dimensions: 328dp × 80dp
- Background: White, Corner Radius: 16dp, Elevation: 1dp
- Padding: 16dp horizontal, 12dp vertical

Action Buttons:
- Height: 56dp, Width: 130dp each (Urdu)
- Spacing: 12dp, Corner Radius: 12dp
- Scroll Direction: Right to left

New QR Button:
- Background: Burnt Orange (#E67E22), Icon: qr_code, 20dp, White
- Text: "نیا QR", Typography: Caption (14sp Medium), White

Analytics Button:
- Background: Surface Tan (#F5E1A4), Icon: analytics, 20dp, Charcoal (#333333)
- Text: "رپورٹس", Typography: Caption (14sp Medium), Charcoal (#333333)

Reviews Button:
- Background: Surface Tan (#F5E1A4), Icon: star, 20dp, Charcoal (#333333)
- Text: "جائزے", Typography: Caption (14sp Medium), Charcoal (#333333)

**براہ راست سرگرمی فیڈ (16dp, 612dp → 344dp, 720dp)**

Container:
- Position: X: 16dp, Y: 612dp
- Dimensions: 328dp × 108dp
- Background: White, Corner Radius: 16dp, Elevation: 1dp
- Padding: 16dp all sides

Section Header:
- Position: X: 312dp, Y: 628dp (RTL positioning)
- Text: "براہ راست سرگرمی"
- Typography: H3 Headline (22sp Bold), Charcoal (#333333), Right align

Auto-refresh Indicator:
- Position: X: 32dp, Y: 632dp (RTL positioning)
- Icon: refresh, 16dp, Success Sage (#27AE60)
- Animation: Gentle rotation every 30 seconds

Activity Item:
- Position: X: 312dp, Y: 652dp (RTL positioning)
- Height: 48dp

Customer Avatar:
- Position: X: 312dp, Y: 652dp (RTL positioning)
- Dimensions: 32dp × 32dp
- Background: Golden Brown (#C49E69) circle, Icon: person, 20dp, White
- Corner Radius: 16dp

Activity Text:
- Position: X: 272dp, Y: 656dp (RTL positioning)
- Text: "احمد خان نے 2 روٹی خریدی (PKR 60)"
- Typography: Body Medium (16sp Regular), Charcoal (#333333)
- Max Width: 312dp, Right align

Timestamp:
- Position: X: 272dp, Y: 676dp (RTL positioning)
- Text: "2 منٹ پہلے"
- Typography: Caption (14sp Regular), Warm Gray (#8D6E63), Right align

Transaction Badge:
- Position: X: 32dp, Y: 660dp (RTL positioning)
- Background: Success Sage (#27AE60)
- Text: "ادا شدہ", Typography: Caption (12sp Bold), White
- Padding: 4dp horizontal, 2dp vertical, Corner Radius: 4dp

**Bottom Navigation (0dp, 720dp → 360dp, 800dp)**

Navigation Items:
- Dashboard (Active): Icon: dashboard, Burnt Orange (#E67E22), Text: "ڈیش بورڈ"
- Analytics: Icon: analytics, Warm Gray (#8D6E63), Text: "تجزیات"
- Orders: Icon: receipt_long, Warm Gray (#8D6E63), Text: "آرڈرز"
- Settings: Icon: settings, Warm Gray (#8D6E63), Text: "ترتیبات"

---
prompt5:
## Donor Impact & Donation Flows (Bilingual Enhanced)

### Screen 1: Donor Dashboard
**Canvas Size:** 360dp × 800dp | **Background:** Background Cream (#FAF3E0)

#### Hero Impact Section (0dp, 44dp → 360dp, 244dp)
**Container:**
- Position: X: 0dp, Y: 44dp
- Dimensions: 360dp × 200dp
- Background: Linear gradient from Success Sage (#27AE60) to Golden Brown (#C49E69)
- Elevation: 4dp

**Impact Counter Display:**
- Position: X: 180dp, Y: 104dp (centered)
- Text: "1,247" (same in both languages)
- Typography: H1 Display (48sp Bold English / 52sp Bold Urdu), White
- Text Align: Center

**Impact Label (Bilingual):**
- Position: X: 180dp, Y: 152dp (centered)
- **English Text:** "Meals Provided This Month" | **Urdu Text:** "اس مہینے فراہم کردہ کھانے"
- Typography: Body Large (18sp Regular English / 20sp Regular Urdu), White with 90% opacity
- Text Align: Center
- Max Width: 296dp English / 385dp Urdu (30% expansion)

**Supporting Text (Bilingual):**
- Position: X: 180dp, Y: 180dp (centered)
- **English Text:** "Your donations have fed 312 laborers" | **Urdu Text:** "آپ کے عطیات نے 312 مزدوروں کو کھانا کھلایا"
- Typography: Body Medium (16sp Regular English / 18sp Regular Urdu), White with 80% opacity
- Text Align: Center
- Max Width: 296dp English / 385dp Urdu

**Monthly Progress:**
- Position: X: 32dp, Y: 208dp
- Dimensions: 296dp × 8dp
- Background: White with 30% opacity
- Progress: White
- Progress Value: 62% of monthly goal
- **Progress Direction:** Left-to-right (LTR) / Right-to-left (RTL)

#### Geographic Impact Map (0dp, 244dp → 360dp, 484dp) - Bilingual
**Container:**
- Position: X: 0dp, Y: 244dp
- Dimensions: 360dp × 240dp
- Background: Surface Tan (#F5E1A4)
- Padding: 16dp all sides

**Map Header (Bilingual):**
- Position: X: 32dp, Y: 260dp (LTR) / X: 312dp, Y: 260dp (RTL)
- **English Text:** "Impact Across Pakistan" | **Urdu Text:** "پاکستان بھر میں اثرات"
- Typography: H3 Headline (20sp Medium English / 24sp Bold Urdu), Charcoal (#333333)
- Text Alignment: Left (LTR) / Right (RTL)

**Map Container:**
- Position: X: 32dp, Y: 292dp
- Dimensions: 296dp × 160dp
- Background: White
- Corner Radius: 12dp
- Elevation: 2dp
- **Note:** Map orientation remains constant (geographic accuracy maintained)

**Map Markers:**
- **High Impact Areas:** Success Sage (#27AE60) markers
- **Medium Impact Areas:** Golden Brown (#C49E69) markers
- **New Areas:** Burnt Orange (#E67E22) markers
- **Heat Map Overlay:** Gradient from Success Sage to Golden Brown

**Impact Legend (Bilingual):**
- Position: X: 32dp, Y: 460dp (LTR) / X: 312dp, Y: 460dp (RTL)
- Layout: Horizontal, 3 items
- **High Impact:** Success Sage dot + **English:** "500+ meals" | **Urdu:** "500+ کھانے"
- **Medium Impact:** Golden Brown dot + **English:** "100-499 meals" | **Urdu:** "100-499 کھانے"
- **New Areas:** Burnt Orange dot + **English:** "1-99 meals" | **Urdu:** "1-99 کھانے"
- Text Alignment: Left (LTR) / Right (RTL)

#### Donation Management Section (0dp, 484dp → 360dp, 644dp) - Bilingual
**Container:**
- Position: X: 0dp, Y: 484dp
- Dimensions: 360dp × 160dp
- Background: White
- Padding: 16dp all sides

**Section Header (Bilingual):**
- Position: X: 32dp, Y: 500dp (LTR) / X: 312dp, Y: 500dp (RTL)
- **English Text:** "Quick Donate" | **Urdu Text:** "فوری عطیہ"
- Typography: H3 Headline (20sp Medium English / 24sp Bold Urdu), Charcoal (#333333)
- Text Alignment: Left (LTR) / Right (RTL)

**Preset Amount Buttons (Bilingual):**
- Position: X: 32dp, Y: 532dp
- Layout: 3 buttons in horizontal row
- Button Size: (296dp - 16dp) / 3 × 56dp (English) / (385dp - 16dp) / 3 × 56dp (Urdu - 30% expansion)
- Spacing: 8dp between buttons
- **Button Order:** Left-to-right (LTR) / Right-to-left (RTL)

**PKR 500 Button (Bilingual):**
- Background: Surface Tan (#F5E1A4)
- Text: "PKR 500" (same in both languages - currency format)
- Typography: Body Medium (16sp Medium English / 18sp Medium Urdu), Charcoal (#333333)
- Corner Radius: 28dp (pill shape)
- **English Subtitle:** "5 meals" | **Urdu Subtitle:** "5 کھانے"
- Subtitle Typography: Caption (12sp Regular English / 14sp Regular Urdu), Warm Gray (#8D6E63)

**PKR 1000 Button (Bilingual):**
- Background: Burnt Orange (#E67E22)
- Text: "PKR 1000" (same in both languages - currency format)
- Typography: Body Medium (16sp Medium English / 18sp Medium Urdu), White
- Corner Radius: 28dp
- **English Subtitle:** "10 meals" | **Urdu Subtitle:** "10 کھانے"
- Subtitle Typography: Caption (12sp Regular English / 14sp Regular Urdu), White with 80% opacity

**PKR 2000 Button (Bilingual):**
- Background: Success Sage (#27AE60)
- Text: "PKR 2000" (same in both languages - currency format)
- Typography: Body Medium (16sp Medium English / 18sp Medium Urdu), White
- Corner Radius: 28dp
- **English Subtitle:** "20 meals" | **Urdu Subtitle:** "20 کھانے"
- Subtitle Typography: Caption (12sp Regular English / 14sp Regular Urdu), White with 80% opacity

**Custom Amount Button (Bilingual):**
- Position: X: 32dp, Y: 604dp
- Dimensions: 296dp × 48dp (English) / 385dp × 48dp (Urdu - 30% expansion)
- Background: Background Cream (#FAF3E0)
- Border: 2dp solid Burnt Orange (#E67E22)
- Corner Radius: 8dp
- **English Text:** "Enter Custom Amount" | **Urdu Text:** "اپنی مرضی کی رقم درج کریں"
- Typography: Body Medium (16sp Medium English / 18sp Medium Urdu), Burnt Orange (#E67E22)
- **Accessibility Label:** "Enter custom donation amount" | "اپنی مرضی کی عطیہ کی رقم درج کریں"

#### Recent Donations History (0dp, 644dp → 360dp, 720dp) - Bilingual
**Container:**
- Position: X: 0dp, Y: 644dp
- Dimensions: 360dp × 76dp
- Background: Surface Tan (#F5E1A4)
- Padding: 16dp all sides

**Section Header (Bilingual):**
- Position: X: 32dp, Y: 660dp (LTR) / X: 312dp, Y: 660dp (RTL)
- **English Text:** "Recent Donations" | **Urdu Text:** "حالیہ عطیات"
- Typography: H3 Headline (18sp Medium English / 22sp Bold Urdu), Charcoal (#333333)
- Text Alignment: Left (LTR) / Right (RTL)

**View All Button (Bilingual):**
- Position: X: 280dp, Y: 664dp (LTR) / X: 32dp, Y: 664dp (RTL)
- **English Text:** "View All" | **Urdu Text:** "سب دیکھیں"
- Typography: Body Small (14sp Medium English / 16sp Medium Urdu), Burnt Orange (#E67E22)
- Touch Target: 64dp × 32dp
- Text Alignment: Right (LTR) / Left (RTL)
- **Accessibility Label:** "View all donations" | "تمام عطیات دیکھیں"

**Donation Item (Bilingual):**
- Position: X: 32dp, Y: 684dp (LTR) / X: 312dp, Y: 684dp (RTL)
- Icon: check_circle, 16dp, Success Sage (#27AE60)
- **English Text:** "PKR 1000 • 3 days ago • 10 meals provided" | **Urdu Text:** "PKR 1000 • 3 دن پہلے • 10 کھانے فراہم کیے"
- Typography: Body Small (14sp Regular English / 16sp Regular Urdu), Warm Gray (#8D6E63)
- Text Alignment: Left (LTR) / Right (RTL)
- Max Width: 240dp English / 312dp Urdu

### Screen 2: Donation Process Flow (Bilingual Enhanced)
**Canvas Size:** 360dp × 800dp | **Background:** Background Cream (#FAF3E0)

#### Donation Header (0dp, 44dp → 360dp, 124dp) - Bilingual
**Container:**
- Position: X: 0dp, Y: 44dp
- Dimensions: 360dp × 80dp
- Background: Burnt Orange (#E67E22)
- Elevation: 4dp

**Back Button:**
- Position: X: 16dp, Y: 68dp (LTR) / X: 312dp, Y: 68dp (RTL)
- Dimensions: 32dp × 32dp
- Icon: arrow_back, 24dp, White (LTR) / arrow_forward, 24dp, White (RTL)
- Touch Target: 48dp × 48dp
- **Accessibility Label:** "Go back" | "واپس جائیں"

**Title (Bilingual):**
- Position: X: 64dp, Y: 72dp (LTR) / X: 264dp, Y: 72dp (RTL)
- **English Text:** "Make a Donation" | **Urdu Text:** "عطیہ کریں"
- Typography: H2 Headline (24sp Bold English / 28sp Bold Urdu), White
- Text Alignment: Left (LTR) / Right (RTL)

**Security Badge (Bilingual):**
- Position: X: 280dp, Y: 72dp (LTR) / X: 32dp, Y: 72dp (RTL)
- Icon: security, 20dp, White
- **English Text:** "Secure" | **Urdu Text:** "محفوظ"
- Typography: Caption (12sp Regular English / 14sp Regular Urdu), White
- Text Alignment: Right (LTR) / Left (RTL)

#### Amount Selection (0dp, 124dp → 360dp, 324dp) - Bilingual
**Container:**
- Position: X: 0dp, Y: 124dp
- Dimensions: 360dp × 200dp
- Background: White
- Padding: 24dp all sides

**Amount Input:**
- Position: X: 48dp, Y: 148dp (LTR) / X: 48dp, Y: 148dp (RTL - centered)
- Dimensions: 264dp × 80dp (English) / 343dp × 80dp (Urdu - 30% expansion)
- Background: Surface Tan (#F5E1A4)
- Corner Radius: 12dp
- Border: 2dp solid Burnt Orange (#E67E22)

**Currency Label:**
- Position: X: 64dp, Y: 168dp (LTR) / X: 295dp, Y: 168dp (RTL)
- Text: "PKR" (same in both languages)
- Typography: Body Large (18sp Medium English / 20sp Medium Urdu), Warm Gray (#8D6E63)

**Amount Input Field:**
- Position: X: 112dp, Y: 160dp (LTR) / X: 243dp, Y: 160dp (RTL)
- Text: "1000" (same in both languages - numeric)
- Typography: H1 Headline (32sp Bold English / 36sp Bold Urdu), Charcoal (#333333)
- Input Type: Numeric
- Max Width: 150dp English / 195dp Urdu
- Text Alignment: Left (LTR) / Right (RTL)

**Impact Calculator:**
- Position: X: 48dp, Y: 244dp
- Text: "This will provide 10 meals to laborers"
- Typography: Body Medium (16sp Regular), Success Sage (#27AE60)
- Icon: restaurant, 20dp, Success Sage (#27AE60)

**Suggested Amounts:**
- Position: X: 48dp, Y: 276dp
- Layout: 4 chips, horizontal scroll
- Chip Size: 80dp × 32dp each
- Spacing: 8dp

**Chip Values:** "500", "1000", "1500", "2000"
- Background: Surface Tan (#F5E1A4)
- Text: Charcoal (#333333)
- Active Background: Burnt Orange (#E67E22)
- Active Text: White

#### Payment Method Selection (0dp, 324dp → 360dp, 564dp)
**Container:**
- Position: X: 0dp, Y: 324dp
- Dimensions: 360dp × 240dp
- Background: Surface Tan (#F5E1A4)
- Padding: 16dp all sides

**Section Header:**
- Position: X: 32dp, Y: 340dp
- Text: "Choose Payment Method"
- Typography: H3 Headline (20sp Medium), Charcoal (#333333)

**Payment Method Grid:**
- Position: X: 32dp, Y: 372dp
- Grid: 2×2 layout
- Card Size: 148dp × 80dp each
- Gap: 12dp

**EasyPaisa Card:**
- Background: White
- Corner Radius: 12dp, Elevation: 1dp
- Logo: EasyPaisa brand colors
- Text: "EasyPaisa"
- Subtitle: "Instant"

**JazzCash Card:**
- Background: White
- Corner Radius: 12dp, Elevation: 1dp
- Logo: JazzCash brand colors
- Text: "JazzCash"
- Subtitle: "Instant"

**Bank Transfer Card:**
- Background: White
- Corner Radius: 12dp, Elevation: 1dp
- Icon: account_balance, 32dp, Burnt Orange (#E67E22)
- Text: "Bank Transfer"
- Subtitle: "1-2 hours"

**Card Payment Card:**
- Background: White
- Corner Radius: 12dp, Elevation: 1dp
- Icon: credit_card, 32dp, Burnt Orange (#E67E22)
- Text: "Credit/Debit Card"
- Subtitle: "Instant"

#### Donation Summary (0dp, 564dp → 360dp, 664dp)
**Container:**
- Position: X: 0dp, Y: 564dp
- Dimensions: 360dp × 100dp
- Background: Success Sage (#27AE60)
- Corner Radius: 16dp (top corners)
- Padding: 20dp all sides

**Amount Display:**
- Position: X: 180dp, Y: 584dp (centered)
- Text: "PKR 1,000"
- Typography: H1 Headline (32sp Bold), White
- Text Align: Center

**Impact Statement:**
- Position: X: 180dp, Y: 616dp (centered)
- Text: "Provides 10 meals to laborers"
- Typography: Body Medium (16sp Regular), White with 90% opacity
- Text Align: Center

**Processing Fee:**
- Position: X: 180dp, Y: 636dp (centered)
- Text: "Processing fee: PKR 25"
- Typography: Caption (12sp Regular), White with 80% opacity
- Text Align: Center

#### Confirm Donation Button (0dp, 664dp → 360dp, 720dp)
**Container:**
- Position: X: 0dp, Y: 664dp
- Dimensions: 360dp × 56dp
- Padding: 16dp horizontal

**Confirm Button:**
- Position: X: 32dp, Y: 680dp
- Dimensions: 296dp × 56dp
- Background: Charcoal (#333333)
- Corner Radius: 8dp
- Elevation: 2dp
- Typography: Body Medium (16sp Medium), White
- Text: "Confirm Donation"
- Icon: favorite, 20dp, White

---

## DONOR DASHBOARD - ENGLISH VERSION

### Screen 1: Donor Dashboard (English)
**Canvas Size:** 360dp × 800dp | **Background:** Background Cream (#FAF3E0)

#### Hero Impact Section (0dp, 44dp → 360dp, 244dp)
**Container:**
- Position: X: 0dp, Y: 44dp
- Dimensions: 360dp × 200dp
- Background: Linear gradient from Success Sage (#27AE60) to Golden Brown (#C49E69)
- Elevation: 4dp

**Impact Counter Display:**
- Position: X: 180dp, Y: 104dp (centered)
- Text: "1,247"
- Typography: H1 Display (48sp Bold), White
- Text Align: Center

**Impact Label:**
- Position: X: 180dp, Y: 152dp (centered)
- Text: "Meals Provided This Month"
- Typography: Body Large (18sp Regular), White with 90% opacity
- Text Align: Center
- Max Width: 296dp

**Supporting Text:**
- Position: X: 180dp, Y: 180dp (centered)
- Text: "Your donations have fed 312 laborers"
- Typography: Body Medium (16sp Regular), White with 80% opacity
- Text Align: Center
- Max Width: 296dp

**Monthly Progress:**
- Position: X: 32dp, Y: 208dp
- Dimensions: 296dp × 8dp
- Background: White with 30% opacity
- Progress: White
- Progress Value: 62% of monthly goal
- Progress Direction: Left-to-right

#### Geographic Impact Map (0dp, 244dp → 360dp, 484dp)
**Container:**
- Position: X: 0dp, Y: 244dp
- Dimensions: 360dp × 240dp
- Background: Surface Tan (#F5E1A4)
- Padding: 16dp all sides

**Map Header:**
- Position: X: 32dp, Y: 260dp
- Text: "Impact Across Pakistan"
- Typography: H3 Headline (20sp Medium), Charcoal (#333333)
- Text Alignment: Left

**Map Container:**
- Position: X: 32dp, Y: 292dp
- Dimensions: 296dp × 160dp
- Background: White
- Corner Radius: 12dp
- Elevation: 2dp

**Map Markers:**
- High Impact Areas: Success Sage (#27AE60) markers
- Medium Impact Areas: Golden Brown (#C49E69) markers
- New Areas: Burnt Orange (#E67E22) markers
- Heat Map Overlay: Gradient from Success Sage to Golden Brown

**Impact Legend:**
- Position: X: 32dp, Y: 460dp
- Layout: Horizontal, 3 items
- High Impact: Success Sage dot + "500+ meals"
- Medium Impact: Golden Brown dot + "100-499 meals"
- New Areas: Burnt Orange dot + "1-99 meals"
- Text Alignment: Left

#### Donation Management Section (0dp, 484dp → 360dp, 644dp)
**Container:**
- Position: X: 0dp, Y: 484dp
- Dimensions: 360dp × 160dp
- Background: White
- Padding: 16dp all sides

**Section Header:**
- Position: X: 32dp, Y: 500dp
- Text: "Quick Donate"
- Typography: H3 Headline (20sp Medium), Charcoal (#333333)
- Text Alignment: Left

**Preset Amount Buttons:**
- Position: X: 32dp, Y: 532dp
- Layout: 3 buttons in horizontal row
- Button Size: (296dp - 16dp) / 3 × 56dp
- Spacing: 8dp between buttons
- Button Order: Left-to-right

**PKR 500 Button:**
- Background: Surface Tan (#F5E1A4)
- Text: "PKR 500"
- Typography: Body Medium (16sp Medium), Charcoal (#333333)
- Corner Radius: 28dp (pill shape)
- Subtitle: "5 meals"
- Subtitle Typography: Caption (12sp Regular), Warm Gray (#8D6E63)

**PKR 1000 Button:**
- Background: Burnt Orange (#E67E22)
- Text: "PKR 1000"
- Typography: Body Medium (16sp Medium), White
- Corner Radius: 28dp
- Subtitle: "10 meals"
- Subtitle Typography: Caption (12sp Regular), White with 80% opacity

**PKR 2000 Button:**
- Background: Success Sage (#27AE60)
- Text: "PKR 2000"
- Typography: Body Medium (16sp Medium), White
- Corner Radius: 28dp
- Subtitle: "20 meals"
- Subtitle Typography: Caption (12sp Regular), White with 80% opacity

**Custom Amount Button:**
- Position: X: 32dp, Y: 604dp
- Dimensions: 296dp × 48dp
- Background: Background Cream (#FAF3E0)
- Border: 2dp solid Burnt Orange (#E67E22)
- Corner Radius: 8dp
- Text: "Enter Custom Amount"
- Typography: Body Medium (16sp Medium), Burnt Orange (#E67E22)
- Accessibility Label: "Enter custom donation amount"

#### Recent Donations History (0dp, 644dp → 360dp, 720dp)
**Container:**
- Position: X: 0dp, Y: 644dp
- Dimensions: 360dp × 76dp
- Background: Surface Tan (#F5E1A4)
- Padding: 16dp all sides

**Section Header:**
- Position: X: 32dp, Y: 660dp
- Text: "Recent Donations"
- Typography: H3 Headline (18sp Medium), Charcoal (#333333)
- Text Alignment: Left

**View All Button:**
- Position: X: 280dp, Y: 664dp
- Text: "View All"
- Typography: Body Small (14sp Medium), Burnt Orange (#E67E22)
- Touch Target: 64dp × 32dp
- Text Alignment: Right
- Accessibility Label: "View all donations"

**Donation Item:**
- Position: X: 32dp, Y: 684dp
- Icon: check_circle, 16dp, Success Sage (#27AE60)
- Text: "PKR 1000 • 3 days ago • 10 meals provided"
- Typography: Body Small (14sp Regular), Warm Gray (#8D6E63)
- Text Alignment: Left
- Max Width: 240dp

### Screen 2: Donation Process Flow (English)
**Canvas Size:** 360dp × 800dp | **Background:** Background Cream (#FAF3E0)

#### Donation Header (0dp, 44dp → 360dp, 124dp)
**Container:**
- Position: X: 0dp, Y: 44dp
- Dimensions: 360dp × 80dp
- Background: Burnt Orange (#E67E22)
- Elevation: 4dp

**Back Button:**
- Position: X: 16dp, Y: 68dp
- Dimensions: 32dp × 32dp
- Icon: arrow_back, 24dp, White
- Touch Target: 48dp × 48dp
- Accessibility Label: "Go back"

**Title:**
- Position: X: 64dp, Y: 72dp
- Text: "Make a Donation"
- Typography: H2 Headline (24sp Bold), White
- Text Alignment: Left

**Security Badge:**
- Position: X: 280dp, Y: 72dp
- Icon: security, 20dp, White
- Text: "Secure"
- Typography: Caption (12sp Regular), White
- Text Alignment: Right

#### Amount Selection (0dp, 124dp → 360dp, 324dp)
**Container:**
- Position: X: 0dp, Y: 124dp
- Dimensions: 360dp × 200dp
- Background: White
- Padding: 24dp all sides

**Amount Input:**
- Position: X: 48dp, Y: 148dp
- Dimensions: 264dp × 80dp
- Background: Surface Tan (#F5E1A4)
- Corner Radius: 12dp
- Border: 2dp solid Burnt Orange (#E67E22)

**Currency Label:**
- Position: X: 64dp, Y: 168dp
- Text: "PKR"
- Typography: Body Large (18sp Medium), Warm Gray (#8D6E63)

**Amount Input Field:**
- Position: X: 112dp, Y: 160dp
- Text: "1000"
- Typography: H1 Headline (32sp Bold), Charcoal (#333333)
- Input Type: Numeric
- Max Width: 150dp
- Text Alignment: Left

**Impact Calculator:**
- Position: X: 48dp, Y: 244dp
- Text: "This will provide 10 meals to laborers"
- Typography: Body Medium (16sp Regular), Success Sage (#27AE60)
- Icon: restaurant, 20dp, Success Sage (#27AE60)

**Suggested Amounts:**
- Position: X: 48dp, Y: 276dp
- Layout: 4 chips, horizontal scroll
- Chip Size: 80dp × 32dp each
- Spacing: 8dp
- Chip Values: "500", "1000", "1500", "2000"
- Background: Surface Tan (#F5E1A4)
- Text: Charcoal (#333333)
- Active Background: Burnt Orange (#E67E22)
- Active Text: White

---

## DONOR DASHBOARD - URDU VERSION

### Screen 1: Donor Dashboard (Urdu)
**Canvas Size:** 360dp × 800dp | **Background:** Background Cream (#FAF3E0)

#### Hero Impact Section (0dp, 44dp → 360dp, 244dp)
**Container:**
- Position: X: 0dp, Y: 44dp
- Dimensions: 360dp × 200dp
- Background: Linear gradient from Success Sage (#27AE60) to Golden Brown (#C49E69)
- Elevation: 4dp

**Impact Counter Display:**
- Position: X: 180dp, Y: 104dp (centered)
- Text: "1,247"
- Typography: H1 Display (52sp Bold), White
- Text Align: Center

**Impact Label:**
- Position: X: 180dp, Y: 152dp (centered)
- Text: "اس مہینے فراہم کردہ کھانے"
- Typography: Body Large (20sp Regular), White with 90% opacity
- Text Align: Center
- Max Width: 385dp

**Supporting Text:**
- Position: X: 180dp, Y: 180dp (centered)
- Text: "آپ کے عطیات نے 312 مزدوروں کو کھانا کھلایا"
- Typography: Body Medium (18sp Regular), White with 80% opacity
- Text Align: Center
- Max Width: 385dp

**Monthly Progress:**
- Position: X: 32dp, Y: 208dp
- Dimensions: 296dp × 8dp
- Background: White with 30% opacity
- Progress: White
- Progress Value: 62% of monthly goal
- Progress Direction: Right-to-left

#### Geographic Impact Map (0dp, 244dp → 360dp, 484dp)
**Container:**
- Position: X: 0dp, Y: 244dp
- Dimensions: 360dp × 240dp
- Background: Surface Tan (#F5E1A4)
- Padding: 16dp all sides

**Map Header:**
- Position: X: 312dp, Y: 260dp
- Text: "پاکستان بھر میں اثرات"
- Typography: H3 Headline (24sp Bold), Charcoal (#333333)
- Text Alignment: Right

**Map Container:**
- Position: X: 32dp, Y: 292dp
- Dimensions: 296dp × 160dp
- Background: White
- Corner Radius: 12dp
- Elevation: 2dp

**Map Markers:**
- High Impact Areas: Success Sage (#27AE60) markers
- Medium Impact Areas: Golden Brown (#C49E69) markers
- New Areas: Burnt Orange (#E67E22) markers
- Heat Map Overlay: Gradient from Success Sage to Golden Brown

**Impact Legend:**
- Position: X: 312dp, Y: 460dp
- Layout: Horizontal, 3 items
- High Impact: Success Sage dot + "500+ کھانے"
- Medium Impact: Golden Brown dot + "100-499 کھانے"
- New Areas: Burnt Orange dot + "1-99 کھانے"
- Text Alignment: Right

#### Donation Management Section (0dp, 484dp → 360dp, 644dp)
**Container:**
- Position: X: 0dp, Y: 484dp
- Dimensions: 360dp × 160dp
- Background: White
- Padding: 16dp all sides

**Section Header:**
- Position: X: 312dp, Y: 500dp
- Text: "فوری عطیہ"
- Typography: H3 Headline (24sp Bold), Charcoal (#333333)
- Text Alignment: Right

**Preset Amount Buttons:**
- Position: X: 32dp, Y: 532dp
- Layout: 3 buttons in horizontal row
- Button Size: (385dp - 16dp) / 3 × 56dp
- Spacing: 8dp between buttons
- Button Order: Right-to-left

**PKR 500 Button:**
- Background: Surface Tan (#F5E1A4)
- Text: "PKR 500"
- Typography: Body Medium (18sp Medium), Charcoal (#333333)
- Corner Radius: 28dp (pill shape)
- Subtitle: "5 کھانے"
- Subtitle Typography: Caption (14sp Regular), Warm Gray (#8D6E63)

**PKR 1000 Button:**
- Background: Burnt Orange (#E67E22)
- Text: "PKR 1000"
- Typography: Body Medium (18sp Medium), White
- Corner Radius: 28dp
- Subtitle: "10 کھانے"
- Subtitle Typography: Caption (14sp Regular), White with 80% opacity

**PKR 2000 Button:**
- Background: Success Sage (#27AE60)
- Text: "PKR 2000"
- Typography: Body Medium (18sp Medium), White
- Corner Radius: 28dp
- Subtitle: "20 کھانے"
- Subtitle Typography: Caption (14sp Regular), White with 80% opacity

**Custom Amount Button:**
- Position: X: 32dp, Y: 604dp
- Dimensions: 385dp × 48dp
- Background: Background Cream (#FAF3E0)
- Border: 2dp solid Burnt Orange (#E67E22)
- Corner Radius: 8dp
- Text: "اپنی مرضی کی رقم درج کریں"
- Typography: Body Medium (18sp Medium), Burnt Orange (#E67E22)
- Accessibility Label: "اپنی مرضی کی عطیہ کی رقم درج کریں"

#### Recent Donations History (0dp, 644dp → 360dp, 720dp)
**Container:**
- Position: X: 0dp, Y: 644dp
- Dimensions: 360dp × 76dp
- Background: Surface Tan (#F5E1A4)
- Padding: 16dp all sides

**Section Header:**
- Position: X: 312dp, Y: 660dp
- Text: "حالیہ عطیات"
- Typography: H3 Headline (22sp Bold), Charcoal (#333333)
- Text Alignment: Right

**View All Button:**
- Position: X: 32dp, Y: 664dp
- Text: "سب دیکھیں"
- Typography: Body Small (16sp Medium), Burnt Orange (#E67E22)
- Touch Target: 64dp × 32dp
- Text Alignment: Left
- Accessibility Label: "تمام عطیات دیکھیں"

**Donation Item:**
- Position: X: 312dp, Y: 684dp
- Icon: check_circle, 16dp, Success Sage (#27AE60)
- Text: "PKR 1000 • 3 دن پہلے • 10 کھانے فراہم کیے"
- Typography: Body Small (16sp Regular), Warm Gray (#8D6E63)
- Text Alignment: Right
- Max Width: 312dp

### Screen 2: Donation Process Flow (Urdu)
**Canvas Size:** 360dp × 800dp | **Background:** Background Cream (#FAF3E0)

#### Donation Header (0dp, 44dp → 360dp, 124dp)
**Container:**
- Position: X: 0dp, Y: 44dp
- Dimensions: 360dp × 80dp
- Background: Burnt Orange (#E67E22)
- Elevation: 4dp

**Back Button:**
- Position: X: 312dp, Y: 68dp
- Dimensions: 32dp × 32dp
- Icon: arrow_forward, 24dp, White
- Touch Target: 48dp × 48dp
- Accessibility Label: "واپس جائیں"

**Title:**
- Position: X: 264dp, Y: 72dp
- Text: "عطیہ کریں"
- Typography: H2 Headline (28sp Bold), White
- Text Alignment: Right

**Security Badge:**
- Position: X: 32dp, Y: 72dp
- Icon: security, 20dp, White
- Text: "محفوظ"
- Typography: Caption (14sp Regular), White
- Text Alignment: Left

#### Amount Selection (0dp, 124dp → 360dp, 324dp)
**Container:**
- Position: X: 0dp, Y: 124dp
- Dimensions: 360dp × 200dp
- Background: White
- Padding: 24dp all sides

**Amount Input:**
- Position: X: 48dp, Y: 148dp
- Dimensions: 343dp × 80dp
- Background: Surface Tan (#F5E1A4)
- Corner Radius: 12dp
- Border: 2dp solid Burnt Orange (#E67E22)

**Currency Label:**
- Position: X: 295dp, Y: 168dp
- Text: "PKR"
- Typography: Body Large (20sp Medium), Warm Gray (#8D6E63)

**Amount Input Field:**
- Position: X: 243dp, Y: 160dp
- Text: "1000"
- Typography: H1 Headline (36sp Bold), Charcoal (#333333)
- Input Type: Numeric
- Max Width: 195dp
- Text Alignment: Right

**Impact Calculator:**
- Position: X: 48dp, Y: 244dp
- Text: "یہ مزدوروں کو 10 کھانے فراہم کرے گا"
- Typography: Body Medium (18sp Regular), Success Sage (#27AE60)
- Icon: restaurant, 20dp, Success Sage (#27AE60)

**Suggested Amounts:**
- Position: X: 48dp, Y: 276dp
- Layout: 4 chips, horizontal scroll
- Chip Size: 80dp × 32dp each
- Spacing: 8dp
- Chip Values: "500", "1000", "1500", "2000"
- Background: Surface Tan (#F5E1A4)
- Text: Charcoal (#333333)
- Active Background: Burnt Orange (#E67E22)
- Active Text: White

---
prompt6:
## Payment & Transaction Screens

### Screen 1: Payment Processing
**Canvas Size:** 360dp × 800dp | **Background:** Background Cream (#FAF3E0)

#### Security Header (0dp, 44dp → 360dp, 124dp)
**Container:**
- Position: X: 0dp, Y: 44dp
- Dimensions: 360dp × 80dp
- Background: Charcoal (#333333)
- Elevation: 4dp

**Security Indicators:**
- Position: X: 16dp, Y: 68dp
- SSL Badge: security icon, 20dp, Success Sage (#27AE60)
- Text: "256-bit SSL Encryption"
- Typography: Body Small (14sp Regular), White

**Close Button:**
- Position: X: 312dp, Y: 68dp
- Dimensions: 32dp × 32dp
- Icon: close, 24dp, White
- Touch Target: 48dp × 48dp

#### Payment Method Details (0dp, 124dp → 360dp, 404dp)
**Container:**
- Position: X: 0dp, Y: 124dp
- Dimensions: 360dp × 280dp
- Background: White
- Padding: 24dp all sides

**Selected Method Header:**
- Position: X: 48dp, Y: 148dp
- Text: "EasyPaisa Payment"
- Typography: H2 Headline (24sp Bold), Charcoal (#333333)
- Icon: EasyPaisa logo, 32dp

**Payment Instructions:**
- Position: X: 48dp, Y: 188dp
- Text: "You will be redirected to EasyPaisa to complete payment"
- Typography: Body Medium (16sp Regular), Warm Gray (#8D6E63)
- Max Width: 264dp

**Phone Number Input:**
- Position: X: 48dp, Y: 228dp
- Dimensions: 264dp × 56dp
- Background: Surface Tan (#F5E1A4)
- Border: 2dp solid Burnt Orange (#E67E22)
- Corner Radius: 8dp
- Label: "EasyPaisa Account Number"
- Typography: Body Medium (16sp Regular), Charcoal (#333333)
- Placeholder: "03XX-XXXXXXX"

**PIN Input:**
- Position: X: 48dp, Y: 300dp
- Dimensions: 264dp × 56dp
- Background: Surface Tan (#F5E1A4)
- Border: 2dp solid Burnt Orange (#E67E22)
- Corner Radius: 8dp
- Label: "PIN"
- Typography: Body Medium (16sp Regular), Charcoal (#333333)
- Input Type: Password
- Max Length: 4

**Security Note:**
- Position: X: 48dp, Y: 372dp
- Text: "Your PIN is encrypted and never stored"
- Typography: Caption (12sp Regular), Success Sage (#27AE60)
- Icon: lock, 16dp, Success Sage (#27AE60)

#### Amount Confirmation (0dp, 404dp → 360dp, 564dp)
**Container:**
- Position: X: 0dp, Y: 404dp
- Dimensions: 360dp × 160dp
- Background: Burnt Orange (#E67E22)
- Corner Radius: 16dp
- Elevation: 2dp
- Margin: 16dp all sides

**Amount Display:**
- Position: X: 180dp, Y: 444dp (centered)
- Text: "PKR 1,025"
- Typography: H1 Display (48sp Bold), White
- Text Align: Center

**Breakdown:**
- Position: X: 180dp, Y: 492dp (centered)
- Text: "Donation: PKR 1,000 + Fee: PKR 25"
- Typography: Body Medium (16sp Regular), White with 90% opacity
- Text Align: Center

**Impact Statement:**
- Position: X: 180dp, Y: 516dp (centered)
- Text: "Provides 10 meals to laborers"
- Typography: Body Medium (16sp Regular), White with 90% opacity
- Text Align: Center

**Transaction ID:**
- Position: X: 180dp, Y: 540dp (centered)
- Text: "Transaction ID: TXN123456789"
- Typography: Caption (12sp Regular), White with 80% opacity
- Text Align: Center

#### Payment Actions (0dp, 580dp → 360dp, 720dp)
**Container:**
- Position: X: 0dp, Y: 580dp
- Dimensions: 360dp × 140dp
- Background: Background Cream (#FAF3E0)
- Padding: 24dp all sides

**Confirm Payment Button:**
- Position: X: 48dp, Y: 604dp
- Dimensions: 264dp × 56dp
- Background: Success Sage (#27AE60)
- Corner Radius: 8dp
- Elevation: 2dp
- Typography: Body Medium (16sp Medium), White
- Text: "Confirm Payment"
- Icon: payment, 20dp, White

**Cancel Button:**
- Position: X: 48dp, Y: 676dp
- Dimensions: 264dp × 48dp
- Background: Transparent
- Border: 2dp solid Warm Gray (#8D6E63)
- Corner Radius: 8dp
- Typography: Body Medium (16sp Medium), Warm Gray (#8D6E63)
- Text: "Cancel"

### Screen 2: Transaction Success
**Canvas Size:** 360dp × 800dp | **Background:** Background Cream (#FAF3E0)

#### Success Animation Container (0dp, 120dp → 360dp, 360dp)
**Container:**
- Position: X: 0dp, Y: 120dp
- Dimensions: 360dp × 240dp
- Background: Transparent

**Success Icon:**
- Position: X: 140dp, Y: 160dp (centered)
- Dimensions: 80dp × 80dp
- Background: Success Sage (#27AE60) circle
- Icon: check_circle, 64dp, White
- Corner Radius: 40dp
- Animation: Scale from 0 to 1.2 to 1.0, 600ms

**Confetti Animation:**
- Particles: Burnt Orange (#E67E22) and Success Sage (#27AE60)
- Duration: 2 seconds
- Pattern: Burst from center, falling motion

**Success Message:**
- Position: X: 180dp, Y: 280dp (centered)
- Text: "Payment Successful!"
- Typography: H1 Headline (32sp Bold), Success Sage (#27AE60)
- Text Align: Center

**Gratitude Message:**
- Position: X: 180dp, Y: 320dp (centered)
- Text: "Thank you for supporting our community"
- Typography: Body Large (18sp Regular), Warm Gray (#8D6E63)
- Text Align: Center

#### Transaction Details (0dp, 360dp → 360dp, 560dp)
**Container:**
- Position: X: 0dp, Y: 360dp
- Dimensions: 360dp × 200dp
- Background: White
- Corner Radius: 16dp
- Elevation: 2dp
- Margin: 16dp all sides
- Padding: 24dp all sides

**Amount Paid:**
- Position: X: 48dp, Y: 384dp
- Text: "Amount Paid"
- Typography: Body Medium (16sp Regular), Warm Gray (#8D6E63)
- Value: "PKR 1,025"
- Value Typography: H2 Headline (24sp Bold), Charcoal (#333333)
- Value Position: X: 264dp, Y: 384dp

**Transaction ID:**
- Position: X: 48dp, Y: 424dp
- Text: "Transaction ID"
- Typography: Body Medium (16sp Regular), Warm Gray (#8D6E63)
- Value: "TXN123456789"
- Value Typography: Body Medium (16sp Regular), Charcoal (#333333)
- Value Position: X: 264dp, Y: 424dp

**Date & Time:**
- Position: X: 48dp, Y: 464dp
- Text: "Date & Time"
- Typography: Body Medium (16sp Regular), Warm Gray (#8D6E63)
- Value: "Dec 15, 2024 • 2:30 PM"
- Value Typography: Body Medium (16sp Regular), Charcoal (#333333)
- Value Position: X: 264dp, Y: 464dp

**Payment Method:**
- Position: X: 48dp, Y: 504dp
- Text: "Payment Method"
- Typography: Body Medium (16sp Regular), Warm Gray (#8D6E63)
- Value: "EasyPaisa"
- Value Typography: Body Medium (16sp Regular), Charcoal (#333333)
- Value Position: X: 264dp, Y: 504dp

#### Impact Visualization (0dp, 576dp → 360dp, 656dp)
**Container:**
- Position: X: 0dp, Y: 576dp
- Dimensions: 360dp × 80dp
- Background: Success Sage (#27AE60)
- Corner Radius: 16dp
- Margin: 16dp all sides
- Padding: 20dp all sides

**Impact Icon:**
- Position: X: 48dp, Y: 596dp
- Dimensions: 40dp × 40dp
- Background: White circle
- Icon: restaurant, 24dp, Success Sage (#27AE60)
- Corner Radius: 20dp

**Impact Text:**
- Position: X: 104dp, Y: 600dp
- Text: "Your donation will provide 10 meals"
- Typography: Body Medium (16sp Medium), White
- Max Width: 200dp

**Beneficiary Count:**
- Position: X: 104dp, Y: 620dp
- Text: "Helping 10 laborers today"
- Typography: Body Small (14sp Regular), White with 90% opacity

#### Action Buttons (0dp, 672dp → 360dp, 720dp)
**Container:**
- Position: X: 0dp, Y: 672dp
- Dimensions: 360dp × 48dp
- Padding: 24dp horizontal

**Receipt Button:**
- Position: X: 48dp, Y: 688dp
- Dimensions: 128dp × 48dp
- Background: Surface Tan (#F5E1A4)
- Corner Radius: 8dp
- Typography: Body Medium (16sp Medium), Charcoal (#333333)
- Text: "Get Receipt"
- Icon: receipt, 20dp, Charcoal (#333333)

**Share Button:**
- Position: X: 184dp, Y: 688dp
- Dimensions: 128dp × 48dp
- Background: Burnt Orange (#E67E22)
- Corner Radius: 8dp
- Typography: Body Medium (16sp Medium), White
- Text: "Share Impact"
- Icon: share, 20dp, White

### Screen 3: Transaction Failed
**Canvas Size:** 360dp × 800dp | **Background:** Background Cream (#FAF3E0)

#### Error Animation Container (0dp, 120dp → 360dp, 360dp)
**Container:**
- Position: X: 0dp, Y: 120dp
- Dimensions: 360dp × 240dp
- Background: Transparent

**Error Icon:**
- Position: X: 140dp, Y: 160dp (centered)
- Dimensions: 80dp × 80dp
- Background: Error Coral (#E57373) circle
- Icon: error, 64dp, White
- Corner Radius: 40dp
- Animation: Shake ±8dp horizontal, 300ms

**Error Message:**
- Position: X: 180dp, Y: 280dp (centered)
- Text: "Payment Failed"
- Typography: H1 Headline (32sp Bold), Error Coral (#E57373)
- Text Align: Center

**Error Description:**
- Position: X: 180dp, Y: 320dp (centered)
- Text: "We couldn't process your payment"
- Typography: Body Large (18sp Regular), Warm Gray (#8D6E63)
- Text Align: Center

#### Error Details (0dp, 360dp → 360dp, 500dp)
**Container:**
- Position: X: 0dp, Y: 360dp
- Dimensions: 360dp × 140dp
- Background: White
- Corner Radius: 16dp
- Elevation: 2dp
- Margin: 16dp all sides
- Padding: 24dp all sides

**Error Code:**
- Position: X: 48dp, Y: 384dp
- Text: "Error Code: PAY_001"
- Typography: Body Medium (16sp Regular), Error Coral (#E57373)

**Error Reason:**
- Position: X: 48dp, Y: 408dp
- Text: "Insufficient balance in EasyPaisa account"
- Typography: Body Medium (16sp Regular), Charcoal (#333333)
- Max Width: 264dp

**Support Contact:**
- Position: X: 48dp, Y: 448dp
- Text: "Need help? Contact support at +92-XXX-XXXXXXX"
- Typography: Body Small (14sp Regular), Warm Gray (#8D6E63)
- Max Width: 264dp

#### Recovery Actions (0dp, 516dp → 360dp, 720dp)
**Container:**
- Position: X: 0dp, Y: 516dp
- Dimensions: 360dp × 204dp
- Background: Background Cream (#FAF3E0)
- Padding: 24dp all sides

**Try Again Button:**
- Position: X: 48dp, Y: 540dp
- Dimensions: 264dp × 56dp
- Background: Burnt Orange (#E67E22)
- Corner Radius: 8dp
- Elevation: 2dp
- Typography: Body Medium (16sp Medium), White
- Text: "Try Again"
- Icon: refresh, 20dp, White

**Change Method Button:**
- Position: X: 48dp, Y: 612dp
- Dimensions: 264dp × 48dp
- Background: Surface Tan (#F5E1A4)
- Corner Radius: 8dp
- Typography: Body Medium (16sp Medium), Charcoal (#333333)
- Text: "Change Payment Method"
- Icon: swap_horiz, 20dp, Charcoal (#333333)

**Contact Support Button:**
- Position: X: 48dp, Y: 676dp
- Dimensions: 264dp × 48dp
- Background: Transparent
- Border: 2dp solid Warm Gray (#8D6E63)
- Corner Radius: 8dp
- Typography: Body Medium (16sp Medium), Warm Gray (#8D6E63)
- Text: "Contact Support"
- Icon: support_agent, 20dp, Warm Gray (#8D6E63)

---
prompt7:
## Profile & Settings Pages

### Screen 1: User Profile
**Canvas Size:** 360dp × 800dp | **Background:** Background Cream (#FAF3E0)

#### Profile Header Section (0dp, 44dp → 360dp, 204dp)
**Container:**
- Position: X: 0dp, Y: 44dp
- Dimensions: 360dp × 160dp
- Background: Linear gradient from Burnt Orange (#E67E22) to Golden Brown (#C49E69)
- Elevation: 4dp

**Profile Photo Container:**
- Position: X: 24dp, Y: 68dp
- Dimensions: 80dp × 80dp
- Background: White circle
- Border: 3dp solid White
- Corner Radius: 40dp (circular)
- Elevation: 2dp

**Profile Photo States:**
- **With Photo:** User uploaded image, properly cropped
- **Placeholder:** person icon, 40dp, Warm Gray (#8D6E63)
- **Upload State:** camera_alt icon, 32dp, Burnt Orange (#E67E22)

**User Information:**
- Position: X: 120dp, Y: 76dp
- Available Width: 216dp

**Name:**
- Text: "Ahmad Khan"
- Typography: H2 Headline (24sp Bold), White
- Line Height: 32sp

**Role:**
- Text: "Laborer"
- Typography: Body Medium (16sp Regular), White with 80% opacity
- Line Height: 24sp

**Member Since:**
- Text: "Member since March 2024"
- Typography: Body Small (14sp Regular), White with 60% opacity
- Line Height: 20sp

**Verification Status:**
- Icon: check_circle, 16dp, Success Sage (#27AE60)
- Text: "Verified"
- Typography: Body Small (14sp Regular), Success Sage (#27AE60)

**Edit Profile Button:**
- Position: X: 280dp, Y: 68dp
- Dimensions: 32dp × 32dp
- Background: White with 20% opacity
- Corner Radius: 16dp
- Icon: edit, 20dp, White
- Touch Target: 48dp × 48dp

#### Account Settings Section (0dp, 204dp → 360dp, 444dp)
**Container:**
- Position: X: 0dp, Y: 204dp
- Dimensions: 360dp × 240dp
- Background: White
- Padding: 0dp

**Section Header:**
- Position: X: 16dp, Y: 220dp
- Text: "Account Settings"
- Typography: H3 Headline (20sp Medium), Charcoal (#333333)
- Bottom Margin: 8dp

**Setting Items (56dp height each):**

#### Edit Profile Item (0dp, 252dp → 360dp, 308dp)
**Container:**
- Position: X: 0dp, Y: 252dp
- Dimensions: 360dp × 56dp
- Background: White
- Border Bottom: 1dp solid Surface Tan (#F5E1A4)
- Padding: 16dp horizontal, 12dp vertical

**Icon:**
- Position: X: 16dp, Y: 268dp
- Dimensions: 24dp × 24dp
- Icon: edit, 24dp, Burnt Orange (#E67E22)

**Content:**
- Label Position: X: 56dp, Y: 268dp
- Label Text: "Edit Profile Information"
- Label Typography: Body Medium (16sp Regular), Charcoal (#333333)

**Action:**
- Position: X: 320dp, Y: 268dp
- Icon: chevron_right, 24dp, Warm Gray (#8D6E63)
- Touch Target: 48dp × 48dp

#### Change Password Item (0dp, 308dp → 360dp, 364dp)
**Container:** Same structure as Edit Profile

**Icon:**
- Icon: lock, 24dp, Burnt Orange (#E67E22)

**Content:**
- Label Text: "Change Password"

#### Verification Documents Item (0dp, 364dp → 360dp, 420dp)
**Container:** Same structure as Edit Profile

**Icon:**
- Icon: verified_user, 24dp, Success Sage (#27AE60)

**Content:**
- Label Text: "Identity Verification"

**Status Badge:**
- Position: X: 280dp, Y: 372dp
- Background: Success Sage (#27AE60)
- Text: "Verified"
- Typography: Caption (12sp Regular), White
- Padding: 4dp horizontal, 2dp vertical
- Corner Radius: 4dp

#### Emergency Contacts Item (0dp, 420dp → 360dp, 476dp)
**Container:** Same structure as Edit Profile

**Icon:**
- Icon: emergency, 24dp, Error Coral (#E57373)

**Content:**
- Label Text: "Emergency Contacts"

**Count Badge:**
- Position: X: 280dp, Y: 428dp
- Text: "(2 contacts)"
- Typography: Caption (12sp Regular), Warm Gray (#8D6E63)

#### App Preferences Section (0dp, 460dp → 360dp, 700dp)
**Container:**
- Position: X: 0dp, Y: 460dp
- Dimensions: 360dp × 240dp
- Background: Surface Tan (#F5E1A4)
- Padding: 0dp

**Section Header:**
- Position: X: 16dp, Y: 476dp
- Text: "App Preferences"
- Typography: H3 Headline (20sp Medium), Charcoal (#333333)

#### Language Selection Item (0dp, 508dp → 360dp, 564dp)
**Container:**
- Position: X: 0dp, Y: 508dp
- Dimensions: 360dp × 56dp
- Background: Surface Tan (#F5E1A4)
- Border Bottom: 1dp solid Golden Brown (#C49E69)
- Padding: 16dp horizontal, 12dp vertical

**Icon:**
- Position: X: 16dp, Y: 524dp
- Icon: language, 24dp, Burnt Orange (#E67E22)

**Content:**
- Label: "Language"
- Current: "English"
- Flag Icon: 20dp, positioned after text

**Toggle Switch:**
- Position: X: 280dp, Y: 528dp
- Dimensions: 48dp × 24dp
- Background: Burnt Orange (#E67E22) when active
- Thumb: White circle, 20dp
- States: English/Urdu toggle

#### Notification Controls Item (0dp, 564dp → 360dp, 620dp)
**Container:** Same structure as Language Selection

**Icon:**
- Icon: notifications, 24dp, Burnt Orange (#E67E22)

**Content:**
- Label: "Notifications"
- Subtitle: "Daily reminders, alerts"

**Toggle Switch:**
- Active State: Burnt Orange (#E67E22)
- Inactive State: Warm Gray (#8D6E63)

#### Theme Selection Item (0dp, 620dp → 360dp, 676dp)
**Container:** Same structure as Language Selection

**Icon:**
- Icon: palette, 24dp, Burnt Orange (#E67E22)

**Content:**
- Label: "Theme"
- Current: "Light Mode"

**Options:**
- Light Mode: Default, Background Cream preview
- Dark Mode: Charcoal background preview
- Auto: System setting sync

#### Text Size Item (0dp, 676dp → 360dp, 732dp)
**Container:** Same structure as Language Selection

**Icon:**
- Icon: text_fields, 24dp, Burnt Orange (#E67E22)

**Content:**
- Label: "Text Size"
- Current: "Medium"

**Slider Control:**
- Position: X: 120dp, Y: 692dp
- Dimensions: 160dp × 24dp
- Track: Surface Tan (#F5E1A4)
- Active Track: Burnt Orange (#E67E22)
- Thumb: Burnt Orange (#E67E22) circle, 16dp
- Options: Small → Medium → Large → Extra Large

#### Account Actions Section (0dp, 716dp → 360dp, 800dp)
**Container:**
- Position: X: 0dp, Y: 716dp
- Dimensions: 360dp × 84dp
- Background: Background Cream (#FAF3E0)
- Padding: 16dp all sides

**Logout Button:**
- Position: X: 16dp, Y: 732dp
- Dimensions: 328dp × 48dp
- Background: Surface Tan (#F5E1A4)
- Corner Radius: 8dp
- Typography: Body Medium (16sp Medium), Charcoal (#333333)
- Text: "Sign Out"
- Icon: logout, 20dp, Charcoal (#333333)

**Delete Account Button:**
- Position: X: 16dp, Y: 796dp
- Dimensions: 328dp × 48dp
- Background: Error Coral (#E57373) with 20% opacity
- Corner Radius: 8dp
- Typography: Body Medium (16sp Medium), Error Coral (#E57373)
- Text: "Delete Account"
- Icon: delete_forever, 20dp, Error Coral (#E57373)
- Confirmation Required: Password verification

### Role-Specific Settings Variations

#### Laborer-Specific Settings
**Daily Preferences Section:**
- **Roti Reminder Time:** Time picker for daily allowance notification
- **Preferred Tandoors:** Favorite locations for quick access
- **Walking Distance:** Maximum distance willing to travel (slider: 1-10 km)
- **Dietary Restrictions:** Halal certification requirements toggle

#### Tandoor Owner-Specific Settings
**Business Management Section:**
- **Operating Hours:** Weekly schedule with holiday settings
- **QR Code Refresh:** Automatic vs manual regeneration toggle
- **Staff Permissions:** Role-based access for employees
- **Business Verification:** License and permit upload status

#### Donor-Specific Settings
**Donation Preferences Section:**
- **Recurring Donations:** Frequency and amount settings
- **Impact Reports:** Email frequency for impact updates
- **Tax Documentation:** Automatic receipt generation toggle
- **Donation Limits:** Monthly budget controls with slider

---
prompt8:
## Map & Location Services

### Screen 1: Tandoor Discovery Map
**Canvas Size:** 360dp × 800dp | **Background:** Background Cream (#FAF3E0)

#### Full-Screen Map Container (0dp, 44dp → 360dp, 720dp)
**Container:**
- Position: X: 0dp, Y: 44dp
- Dimensions: 360dp × 676dp
- Background: Map tiles with custom styling
- Overlay Controls: Floating elements with elevation

**Map Style Customization:**
- **Primary Markers:** Burnt Orange (#E67E22) for tandoors
- **Secondary Markers:** Golden Brown (#C49E69) for user location
- **Route Lines:** Charcoal (#333333) with 4dp width
- **Selected State:** Success Sage (#27AE60) with pulsing animation
- **Disabled Markers:** Warm Gray (#8D6E63) with 50% opacity

#### Search Bar (Top Overlay) (16dp, 60dp → 344dp, 116dp)
**Container:**
- Position: X: 16dp, Y: 60dp
- Dimensions: 328dp × 56dp
- Background: Background Cream (#FAF3E0)
- Corner Radius: 28dp
- Elevation: 4dp with Burnt Orange (#E67E22) shadow tint
- Border: 1dp solid Surface Tan (#F5E1A4)

**Search Input Layout:**
- Icon Position: X: 32dp, Y: 76dp
- Icon: search, 24dp, Warm Gray (#8D6E63)
- Text Input Position: X: 64dp, Y: 76dp
- Typography: Body Medium (16sp), Charcoal (#333333)
- Placeholder: "Search tandoors..."
- Clear Button: X: 320dp, Y: 76dp, 32dp × 32dp

#### Filter Controls (16dp, 132dp → 344dp, 180dp)
**Container:**
- Position: X: 16dp, Y: 132dp
- Dimensions: 328dp × 48dp
- Layout: Horizontal scrolling chips
- Spacing: 8dp between chips

**Filter Chip Specifications:**
- Height: 40dp
- Padding: 16dp horizontal, 8dp vertical
- Corner Radius: 20dp (pill shape)
- Typography: Body Small (14sp Medium)

**Filter States:**
- **Default:** Background Surface Tan (#F5E1A4), text Charcoal (#333333)
- **Selected:** Background Burnt Orange (#E67E22), text White
- **Disabled:** Background Warm Gray (#8D6E63) 30% opacity

**Filter Options:**
- "Open Now" - Real-time availability
- "Highest Rated" - 4+ star ratings
- "Closest" - Distance-based sorting
- "Subsidized" - Participating in program
- "24/7" - Always available

#### Tandoor Markers & States
**Default Marker Specifications:**
- Size: 32dp × 32dp
- Background: Burnt Orange (#E67E22) circle
- Icon: store, 20dp, White
- Border: 2dp solid White
- Elevation: 2dp

**Marker State Variations:**

**Available (Green Status):**
- Border Color: Success Sage (#27AE60)
- Pulse Animation: Scale 1.0 → 1.2 → 1.0, 2s infinite

**Busy (Yellow Status):**
- Border Color: Golden Brown (#C49E69)
- No animation, static display

**Closed (Red Status):**
- Background: Warm Gray (#8D6E63)
- Border Color: Error Coral (#E57373)
- Icon Opacity: 50%

**Selected State:**
- Size: 40dp × 40dp (scaled up)
- Background: Burnt Orange (#E67E22)
- Border: 3dp solid Success Sage (#27AE60)
- Elevation: 6dp

#### Bottom Sheet (Tandoor Details) (0dp, 480dp → 360dp, 720dp)
**Container:**
- Position: X: 0dp, Y: 480dp (expandable)
- Dimensions: 360dp × 240dp (collapsed), up to 80% screen height (expanded)
- Background: Background Cream (#FAF3E0)
- Corner Radius: 16dp (top corners only)
- Elevation: 8dp
- Handle: 32dp × 4dp, Warm Gray (#8D6E63), centered at top

**Collapsed State Content (240dp height):**

**Tandoor Header:**
- Position: X: 24dp, Y: 504dp
- Tandoor Name: "Ali's Traditional Tandoor"
- Typography: H2 Headline (24sp Bold), Charcoal (#333333)

**Distance & Time:**
- Position: X: 24dp, Y: 532dp
- Text: "2.3 km • 8 min walk"
- Typography: Body Medium (16sp Regular), Warm Gray (#8D6E63)
- Icon: directions_walk, 16dp, Warm Gray (#8D6E63)

**Rating Display:**
- Position: X: 24dp, Y: 556dp
- 5-star system with Golden Brown (#C49E69)
- Rating: "4.8 (156 reviews)"
- Typography: Body Medium (16sp Regular), Charcoal (#333333)

**Status Indicator:**
- Position: X: 280dp, Y: 508dp
- 16dp circle, color-coded availability
- Text: "Open Now"
- Typography: Body Small (14sp Regular), Success Sage (#27AE60)

**Quick Actions:**
- Position: X: 24dp, Y: 584dp
- Layout: 3 buttons, horizontal
- Button Size: 96dp × 48dp each
- Spacing: 8dp

**Navigate Button:**
- Background: Burnt Orange (#E67E22)
- Icon: directions, 20dp, White
- Text: "Navigate"
- Typography: Caption (12sp Medium), White

**Call Button:**
- Background: Surface Tan (#F5E1A4)
- Icon: phone, 20dp, Charcoal (#333333)
- Text: "Call"
- Typography: Caption (12sp Medium), Charcoal (#333333)

**Favorite Button:**
- Background: Surface Tan (#F5E1A4)
- Icon: favorite_border/favorite, 20dp, Burnt Orange (#E67E22)
- Text: "Save"
- Typography: Caption (12sp Medium), Charcoal (#333333)

**Expanded State Additional Content:**

**Operating Hours Section:**
- Full weekly schedule with current status
- Today's hours highlighted in Burnt Orange (#E67E22)
- Special notes for prayer times and holidays

**Menu Preview:**
- Roti types and prices in PKR
- Subsidized vs regular pricing clearly marked
- Special offers highlighted in Success Sage (#27AE60)

**Recent Reviews:**
- User feedback with ratings
- Review text with star ratings
- "See all reviews" link in Burnt Orange (#E67E22)

**Photos Gallery:**
- Tandoor and bread images
- Horizontal scrolling gallery
- High-quality food photography

**Contact Information:**
- Phone number with direct call button
- Address with copy functionality
- Directions integration with maps app

#### Floating Action Buttons

**My Location Button:**
- Position: X: 304dp, Y: 196dp
- Dimensions: 56dp × 56dp
- Background: Burnt Orange (#E67E22)
- Icon: my_location, 24dp, White
- Corner Radius: 28dp (circular)
- Elevation: 6dp

**Map Type Toggle:**
- Position: X: 304dp, Y: 260dp
- Dimensions: 48dp × 48dp
- Background: Background Cream (#FAF3E0)
- Icon: layers, 20dp, Burnt Orange (#E67E22)
- Corner Radius: 24dp (circular)
- Elevation: 4dp

### Navigation Integration

**Route Display Specifications:**
- **Route Line:** 6dp width, Charcoal (#333333)
- **Route Outline:** 8dp width, White (behind main line)
- **Turn Indicators:** Directional arrows every 100m
- **Distance Markers:** Every 500m with distance labels
- **ETA Display:** Floating card with arrival time

**Voice Navigation Support:**
- **Language Options:** English and Urdu
- **Turn Announcements:** "Turn right in 100 meters" / "100 میٹر میں دائیں مڑیں"
- **Landmark References:** Local landmarks for easier navigation
- **Audio Settings:** Volume control, voice selection

---
prompt9:
## Analytics & Reporting Dashboards

### Screen 1: Business Analytics (Tandoor Owner)
**Canvas Size:** 360dp × 800dp | **Background:** Background Cream (#FAF3E0)

#### Time Selector Header (0dp, 44dp → 360dp, 124dp)
**Container:**
- Position: X: 0dp, Y: 44dp
- Dimensions: 360dp × 80dp
- Background: Background Cream (#FAF3E0)
- Border Bottom: 1dp solid Surface Tan (#F5E1A4)
- Padding: 16dp horizontal, 12dp vertical

**Time Range Selector:**
- Position: X: 16dp, Y: 60dp
- Layout: Horizontal scrolling chips
- Chip Size: Variable width × 40dp
- Spacing: 8dp between chips
- Corner Radius: 20dp (pill shape)

**Time Range Options:**
- **Today:** Default selection, Burnt Orange (#E67E22) background
- **This Week:** Surface Tan (#F5E1A4) background
- **This Month:** Surface Tan (#F5E1A4) background
- **Last 3 Months:** Surface Tan (#F5E1A4) background
- **Custom Range:** Date picker integration

**Export Button:**
- Position: X: 288dp, Y: 60dp
- Dimensions: 56dp × 40dp
- Background: Burnt Orange (#E67E22)
- Icon: download, 20dp, White
- Corner Radius: 20dp

#### Key Metrics Overview (0dp, 124dp → 360dp, 324dp)
**Container:**
- Position: X: 0dp, Y: 124dp
- Dimensions: 360dp × 200dp
- Background: Background Cream (#FAF3E0)
- Padding: 16dp all sides

**Metrics Grid Layout:**
- Grid: 2 columns × 2 rows
- Cell Size: (328dp - 16dp) / 2 × 84dp
- Gap: 16dp horizontal, 16dp vertical

**Individual Metric Cards:**
- Background: White
- Border: 1dp solid Surface Tan (#F5E1A4)
- Corner Radius: 12dp
- Elevation: 1dp
- Padding: 16dp

**Total Revenue Card:**
- Icon: attach_money, 24dp, Success Sage (#27AE60)
- Value: "PKR 45,230"
- Typography: H2 Headline (20sp Bold), Charcoal (#333333)
- Label: "Total Revenue"
- Typography: Body Small (14sp Regular), Warm Gray (#8D6E63)
- Trend: "+15% ↗"
- Trend Color: Success Sage (#27AE60)

**Transactions Count Card:**
- Icon: receipt, 24dp, Burnt Orange (#E67E22)
- Value: "1,247"
- Label: "Total Transactions"
- Trend: "+8% ↗"

**Average Rating Card:**
- Icon: star, 24dp, Golden Brown (#C49E69)
- Value: "4.8"
- Label: "Average Rating"
- Trend: "+0.2 ↗"

**Customer Retention Card:**
- Icon: people, 24dp, Charcoal (#333333)
- Value: "89%"
- Label: "Customer Retention"
- Trend: "-2% ↘"
- Trend Color: Error Coral (#E57373)

#### Interactive Charts Section (0dp, 324dp → 360dp, 724dp)
**Container:**
- Position: X: 0dp, Y: 324dp
- Dimensions: 360dp × 400dp
- Background: White
- Padding: 16dp all sides
- Elevation: 2dp

**Chart Type Selector:**
- Position: X: 16dp, Y: 340dp
- Layout: Horizontal tabs
- Tab Size: Variable width × 40dp
- Active Tab: Burnt Orange (#E67E22) background, White text
- Inactive Tabs: Surface Tan (#F5E1A4) background, Charcoal text

**Chart Types:**
1. **Revenue Trends:** Line chart showing daily/weekly/monthly revenue
2. **Transaction Volume:** Bar chart showing transaction counts
3. **Peak Hours:** Heat map showing busiest times
4. **Customer Demographics:** Pie chart showing user distribution

**Chart Specifications:**
- **Chart Area:** 328dp × 280dp
- **Grid Lines:** 1dp, Surface Tan (#F5E1A4)
- **Data Lines:** 3dp width, Burnt Orange (#E67E22)
- **Data Points:** 8dp circles, Golden Brown (#C49E69)
- **Hover States:** 12dp circles with tooltip
- **Axis Labels:** Body Small (14sp), Warm Gray (#8D6E63)

#### Revenue Trends Chart (Line Chart)
**Visual Specifications:**
- **Primary Line:** Revenue over time, 3dp width, Burnt Orange (#E67E22)
- **Secondary Line:** Subsidized vs regular sales, 2dp width, Golden Brown (#C49E69)
- **Fill Area:** Gradient from Burnt Orange to transparent
- **Data Points:** Interactive with value tooltips
- **Y-Axis:** Revenue in PKR with proper formatting
- **X-Axis:** Time periods based on selected range

**Interactive Features:**
- **Zoom:** Pinch-to-zoom for detailed view
- **Pan:** Horizontal scrolling for extended periods
- **Tooltip:** Tap data points for exact values
- **Legend:** Toggle line visibility
- **Export:** Chart as image or data as CSV

---
prompt10:
## Component Library & Interactions

### Button Component System

#### Primary Button
**Default State:**
- Dimensions: Variable width × 56dp height
- Background: Burnt Orange (#E67E22)
- Corner Radius: 8dp
- Elevation: 2dp
- Typography: Body Medium (16sp Medium), White
- Padding: 24dp horizontal, 16dp vertical
- Icon Support: 20dp icon with 8dp spacing from text

**Interaction States:**
- **Hover:** Background darkens to #D35400, elevation increases to 3dp
- **Pressed:** Scale 0.98x, elevation decreases to 1dp, duration 100ms
- **Disabled:** Background Surface Tan (#F5E1A4), text Warm Gray (#8D6E63), 60% opacity
- **Loading:** Spinner animation, text hidden, background maintained

#### Secondary Button
**Default State:**
- Dimensions: Variable width × 48dp height
- Background: Surface Tan (#F5E1A4)
- Border: 2dp solid Burnt Orange (#E67E22)
- Corner Radius: 8dp
- Elevation: 1dp
- Typography: Body Medium (16sp Medium), Charcoal (#333333)

**Interaction States:**
- **Hover:** Background lightens, border color intensifies
- **Pressed:** Scale 0.98x, background darkens slightly
- **Disabled:** Border Warm Gray (#8D6E63), text 60% opacity

#### Text Button
**Default State:**
- Dimensions: Variable width × 40dp height
- Background: Transparent
- Corner Radius: 4dp
- Typography: Body Medium (16sp Medium), Burnt Orange (#E67E22)
- Padding: 16dp horizontal, 8dp vertical

**Interaction States:**
- **Hover:** Background Burnt Orange (#E67E22) 10% opacity
- **Pressed:** Background Burnt Orange (#E67E22) 20% opacity
- **Disabled:** Text Warm Gray (#8D6E63) 60% opacity

#### Floating Action Button (FAB)
**Default State:**
- Dimensions: 56dp × 56dp
- Background: Burnt Orange (#E67E22)
- Corner Radius: 28dp (circular)
- Elevation: 6dp
- Icon: 24dp, White

**Interaction States:**
- **Hover:** Scale 1.05x, elevation 8dp
- **Pressed:** Scale 0.95x, elevation 4dp
- **Disabled:** Background Warm Gray (#8D6E63), 60% opacity

### Input Component System

#### Text Input Field
**Default State:**
- Dimensions: Variable width × 56dp height
- Background: Background Cream (#FAF3E0)
- Border: 2dp solid Surface Tan (#F5E1A4)
- Corner Radius: 8dp
- Typography: Body Medium (16sp Regular), Charcoal (#333333)
- Padding: 16dp horizontal, 16dp vertical

**Label:**
- Typography: Body Small (14sp Regular), Warm Gray (#8D6E63)
- Position: Above input with 4dp spacing

**Placeholder:**
- Typography: Body Medium (16sp Regular), Warm Gray (#8D6E63) 60% opacity

**Interaction States:**
- **Focus:** Border color changes to Burnt Orange (#E67E22), label color to Burnt Orange
- **Error:** Border color Error Coral (#E57373), helper text in Error Coral
- **Success:** Border color Success Sage (#27AE60), helper text in Success Sage
- **Disabled:** Background Warm Gray (#8D6E63) 20% opacity, text 60% opacity

#### Search Input
**Default State:**
- Dimensions: Variable width × 48dp height
- Background: Background Cream (#FAF3E0)
- Border: 1dp solid Surface Tan (#F5E1A4)
- Corner Radius: 28dp (pill shape)
- Typography: Body Medium (16sp Regular), Charcoal (#333333)

**Search Icon:**
- Position: 16dp from left edge
- Icon: search, 20dp, Warm Gray (#8D6E63)

**Clear Button:**
- Position: 16dp from right edge
- Dimensions: 32dp × 32dp
- Icon: close, 16dp, Warm Gray (#8D6E63)
- Visible only when text is present

### Card Component System

#### Standard Card
**Default State:**
- Background: Background Cream (#FAF3E0)
- Border: 1dp solid Surface Tan (#F5E1A4)
- Corner Radius: 12dp
- Elevation: 1dp
- Padding: 16dp all sides

**Content Structure:**
- Header: H3 Headline (20sp Medium), Charcoal (#333333)
- Body: Body Medium (16sp Regular), Warm Gray (#8D6E63)
- Actions: Button components with 8dp top margin

**Interaction States:**
- **Hover:** Elevation increases to 2dp, scale 1.01x
- **Pressed:** Scale 0.99x, elevation 0dp
- **Selected:** Border color Burnt Orange (#E67E22), elevation 3dp

#### Elevated Card
**Default State:**
- Background: Surface Tan (#F5E1A4)
- Border: None
- Corner Radius: 16dp
- Elevation: 4dp with Burnt Orange shadow tint
- Padding: 24dp all sides

**Usage:** Important content, featured items, call-to-action cards

#### Metric Card
**Default State:**
- Background: Surface Tan (#F5E1A4)
- Border: 1dp solid Golden Brown (#C49E69)
- Corner Radius: 12dp
- Elevation: 2dp
- Dimensions: 156dp × 152dp (for 2×2 grid)
- Padding: 16dp all sides

**Content Structure:**
- Icon: 32dp, positioned top-left with brand color background
- Value: H1 Headline (28sp Bold), Charcoal (#333333)
- Label: Body Medium (16sp Regular), Warm Gray (#8D6E63)
- Trend: Caption (12sp Regular), Success Sage or Error Coral

### Navigation Component System

#### Bottom Navigation
**Container:**
- Dimensions: 360dp × 80dp
- Background: Background Cream (#FAF3E0)
- Elevation: 8dp
- Border Top: 1dp solid Surface Tan (#F5E1A4)

**Navigation Items:**
- Layout: 3-5 items, equally distributed
- Item Dimensions: Variable width × 64dp height
- Touch Target: Full item area

**Item States:**
- **Active:** Icon and label in Burnt Orange (#E67E22)
- **Inactive:** Icon and label in Warm Gray (#8D6E63)
- **Pressed:** Background Burnt Orange (#E67E22) 10% opacity

**Typography:**
- Label: Caption (12sp Regular)
- Icon: 24dp

#### Top App Bar
**Container:**
- Dimensions: 360dp × 64dp
- Background: Burnt Orange (#E67E22)
- Elevation: 4dp

**Content:**
- Title: H2 Headline (24sp Bold), White
- Navigation Icon: 24dp, White, 16dp from left
- Action Icons: 24dp, White, 16dp from right
- Touch Targets: 48dp × 48dp minimum

### Animation Specifications

#### Micro-Interactions
**Button Press Animation:**
- Scale: 1.0 → 0.98 → 1.0
- Duration: 100ms ease-out
- Trigger: Touch down/up

**Loading States:**
- Spinner: 360° rotation, 1s linear infinite
- Skeleton: Shimmer effect with Surface Tan (#F5E1A4)
- Progress: Smooth fill animation with Burnt Orange (#E67E22)

**State Transitions:**
- Color Changes: 200ms ease-in-out
- Scale Changes: 150ms ease-out
- Opacity Changes: 300ms ease-in-out

#### Page Transitions
**Screen Navigation:**
- Forward: Slide left, 300ms ease-out
- Back: Slide right, 250ms ease-in
- Modal: Scale up from center, 250ms ease-out
- Modal Dismiss: Scale down to center, 200ms ease-in

#### Success/Error Feedback
**Success Animation:**
- Check icon: Scale 0 → 1.2 → 1.0, 600ms
- Background: Success Sage (#27AE60) pulse
- Haptic: Success pattern

**Error Animation:**
- Shake: ±8dp horizontal, 300ms
- Background: Error Coral (#E57373) flash
- Haptic: Error pattern

### Accessibility Specifications

#### Screen Reader Support
**Semantic Structure:**
- Proper heading hierarchy (H1 → H2 → H3)
- ARIA labels for all interactive elements
- Live regions for dynamic content
- Alternative text for images and icons

**Focus Management:**
- Logical tab order throughout interface
- Focus indicators: 2dp outline in Burnt Orange (#E67E22)
- Focus trapping in modals and overlays
- Skip links for main content areas

#### Touch Accessibility
**Target Sizes:**
- Minimum: 48dp × 48dp (WCAG AA)
- Recommended: 56dp × 56dp (primary actions)
- Work Environment: 64dp × 64dp (outdoor use)
- Adjacent Spacing: 8dp minimum between targets

**Gesture Support:**
- Alternative input methods for complex gestures
- Voice commands for primary actions
- Switch control compatibility
- Reduced motion respect

#### Visual Accessibility
**Color Contrast:**
- All text combinations exceed 4.5:1 ratio
- Interactive elements meet 3:1 ratio for non-text
- Color-independent information conveyance
- High contrast mode support

**Text Scaling:**
- Support up to 200% system font scaling
- Responsive layout adaptation
- Maintained functionality at all scales
- Proper text wrapping and truncation

### Cultural Considerations

#### Pakistani Context
**Visual Design:**
- Professional aesthetics suitable for business use
- Color choices respect Islamic cultural preferences
- Imagery appropriate for diverse religious backgrounds
- Traditional patterns and motifs where appropriate

**Language Support:**
- Complete English/Urdu bilingual interface
- Proper RTL layout for Urdu content
- Cultural terminology and expressions
- Local currency formatting (PKR)

**Business Culture:**
- Respectful representation of work and labor
- Emphasis on community service and social responsibility
- Professional hierarchy acknowledgment
- Family-oriented decision making consideration

#### Islamic Design Principles
**Content Guidelines:**
- Respectful imagery avoiding inappropriate depictions
- Appropriate color usage for religious contexts
- Prayer time awareness in scheduling features
- Halal certification prominence where relevant

**Interaction Patterns:**
- Right-to-left reading pattern accommodation
- Cultural gesture and interaction preferences
- Community-focused messaging and features
- Respectful error messaging and guidance

---

## Technical Implementation Guidelines

### Design Token System
```css
:root {
  /* Primary Colors */
  --color-primary-burnt-orange: #E67E22;
  --color-secondary-golden-brown: #C49E69;
  --color-accent-charcoal: #333333;

  /* Background & Surface */
  --color-background-cream: #FAF3E0;
  --color-surface-tan: #F5E1A4;
  --color-text-warm-gray: #8D6E63;

  /* Functional Colors */
  --color-success-sage: #27AE60;
  --color-error-coral: #E57373;
  --color-warning-orange: #E67E22;
  --color-info-blue: #3498DB;

  /* Spacing System */
  --spacing-xs: 4dp;
  --spacing-sm: 8dp;
  --spacing-md: 16dp;
  --spacing-lg: 24dp;
  --spacing-xl: 32dp;
  --spacing-xxl: 48dp;

  /* Typography Scale */
  --font-size-display: 48sp;
  --font-size-h1: 32sp;
  --font-size-h2: 24sp;
  --font-size-h3: 20sp;
  --font-size-body-large: 18sp;
  --font-size-body-medium: 16sp;
  --font-size-body-small: 14sp;
  --font-size-caption: 12sp;

  /* Elevation Shadows */
  --elevation-1: 0 1dp 3dp rgba(230, 126, 34, 0.12);
  --elevation-2: 0 2dp 6dp rgba(230, 126, 34, 0.16);
  --elevation-4: 0 4dp 8dp rgba(230, 126, 34, 0.20);
  --elevation-6: 0 6dp 12dp rgba(230, 126, 34, 0.24);
  --elevation-8: 0 8dp 16dp rgba(230, 126, 34, 0.28);
}
```

### Component Architecture
**Atomic Design Methodology:**
- **Atoms:** Basic elements (buttons, inputs, icons)
- **Molecules:** Simple combinations (search bar, card header)
- **Organisms:** Complex components (navigation, dashboard sections)
- **Templates:** Page layouts and structures
- **Pages:** Specific instances with real content

### Performance Guidelines
**Animation Performance:**
- 60fps target for all animations
- GPU acceleration for transforms and opacity
- Reduced motion respect for accessibility
- Battery optimization considerations

**Asset Optimization:**
- WebP images with PNG fallback
- SVG icons with font fallback
- Optimized for low-bandwidth environments
- Progressive loading strategies

### Quality Assurance Standards
**Testing Requirements:**
- Cross-device compatibility (Android 8+)
- Screen size adaptation (360dp-600dp)
- Accessibility compliance verification
- Cultural appropriateness validation
- Performance benchmarking

**Documentation Standards:**
- Component usage guidelines
- Accessibility implementation notes
- Cultural sensitivity considerations
- Technical implementation specifications
- Version control and change management

---

## Comprehensive Bilingual Content Reference

### Core UI Text Translations

#### Navigation & Actions
| English | Urdu | Context |
|---------|------|---------|
| Get Started | شروع کریں | Primary CTA button |
| Back | واپس | Navigation |
| Next | اگلا | Navigation |
| Continue | جاری رکھیں | Process flow |
| Cancel | منسوخ | Action cancellation |
| Save | محفوظ کریں | Data saving |
| Edit | ترمیم | Content editing |
| Delete | حذف کریں | Content removal |
| Search | تلاش | Search functionality |
| Filter | فلٹر | Content filtering |
| Sort | ترتیب | Content sorting |
| Refresh | تازہ کریں | Data refresh |
| Settings | ترتیبات | App settings |
| Help | مدد | Support |
| About | کے بارے میں | App information |
| Open Scanner | اسکینر کھولیں | QR scanner access |
| My QR Token | میرا QR ٹوکن | Personal QR display |
| Show your daily code | اپنا یومیہ کوڈ دکھائیں | QR token subtitle |
| Emergency | ایمرجنسی | Emergency button |
| Call Roti Help Center? | روٹی ہیلپ سینٹر کو کال کریں؟ | Emergency dialog |
| Call | کال کریں | Confirm call action |

#### User Roles & Identity
| English | Urdu | Context |
|---------|------|---------|
| Laborer | مزدور | User role |
| Tandoor Owner | تندور مالک | User role |
| Donor | عطیہ دہندہ | User role |
| Delivery Personnel | ڈیلیوری عملہ | User role |
| Profile | پروفائل | User profile |
| Account | اکاؤنٹ | User account |
| Verification | تصدیق | Identity verification |
| Verified | تصدیق شدہ | Verification status |
| Member since | ممبر بننے کی تاریخ | Membership date |

#### Business & Transactions
| English | Urdu | Context |
|---------|------|---------|
| Revenue | آمدنی | Business income |
| Transaction | لین دین | Financial transaction |
| Payment | ادائیگی | Payment process |
| Receipt | رسید | Payment receipt |
| Balance | بیلنس | Account balance |
| Daily Sales | یومیہ فروخت | Sales metrics |
| Total Revenue | کل آمدنی | Revenue total |
| Customer Rating | کسٹمر ریٹنگ | Rating system |
| Business Hours | کاروباری اوقات | Operating hours |
| Open Now | اب کھلا ہے | Current status |
| Closed | بند | Current status |

#### Food & Dining
| English | Urdu | Context |
|---------|------|---------|
| Roti | روٹی | Bread product |
| Tandoor | تندور | Bread oven |
| Meal | کھانا | Food serving |
| Subsidized | سبسڈی والا | Subsidized food |
| Regular | عام | Regular pricing |
| Daily Allowance | یومیہ الاؤنس | Daily food quota |
| Menu | مینو | Food menu |
| Order | آرڈر | Food order |
| Delivery | ڈیلیوری | Food delivery |

#### Location & Navigation
| English | Urdu | Context |
|---------|------|---------|
| Location | مقام | Geographic location |
| Address | پتہ | Physical address |
| Distance | فاصلہ | Geographic distance |
| Navigate | راستہ دکھائیں | Navigation action |
| Directions | سمت | Navigation directions |
| Map | نقشہ | Map interface |
| Nearby | قریب | Proximity |
| Find Tandoor | تندور تلاش کریں | Location search |

#### Time & Dates
| English | Urdu | Context |
|---------|------|---------|
| Today | آج | Current day |
| Yesterday | کل | Previous day |
| Tomorrow | کل | Next day |
| This Week | اس ہفتے | Current week |
| This Month | اس مہینے | Current month |
| Morning | صبح | Time of day |
| Afternoon | دوپہر | Time of day |
| Evening | شام | Time of day |
| Night | رات | Time of day |

#### Status & Feedback
| English | Urdu | Context |
|---------|------|---------|
| Success | کامیابی | Success state |
| Error | خرابی | Error state |
| Loading | لوڈ ہو رہا ہے | Loading state |
| Complete | مکمل | Completion status |
| Pending | زیر التواء | Pending status |
| Failed | ناکام | Failure status |
| Available | دستیاب | Availability |
| Unavailable | غیر دستیاب | Unavailability |
| Used | استعمال شدہ | Consumed allowance |
| You've used all roti for today | آپ نے آج کی تمام روٹی استعمال کر لی ہے | Exhausted state message |
| Resets at midnight | آدھی رات کو ری سیٹ ہوگا | Reset time information |
| Open profile | پروفائل کھولیں | Profile access |
| View notifications | اطلاعات دیکھیں | Notification access |

### Islamic & Cultural Phrases
| English | Urdu | Context |
|---------|------|---------|
| Assalam Alaikum | السلام علیکم | Islamic greeting |
| Bismillah | بسم اللہ | Beginning invocation |
| Alhamdulillah | الحمد للہ | Gratitude expression |
| Insha'Allah | ان شاء اللہ | Future hope |
| Masha'Allah | ماشاء اللہ | Appreciation |
| Halal | حلال | Religiously permissible |
| Community Service | کمیونٹی سروس | Social service |
| Helping Others | دوسروں کی مدد | Charitable action |

### Technical Implementation Notes

#### Font Rendering Specifications
**English (Noto Sans):**
- **Character Set:** Latin Basic, Latin-1 Supplement, Latin Extended-A
- **Font Features:** Kerning, ligatures for common pairs
- **Fallback Chain:** Noto Sans → Roboto → System Sans

**Urdu (Noto Nastaliq Urdu):**
- **Character Set:** Arabic, Arabic Supplement, Arabic Extended-A
- **Font Features:** Contextual alternates, ligatures, mark positioning
- **Fallback Chain:** Noto Nastaliq Urdu → Jameel Noori → System Arabic

#### Dynamic Layout Adaptation
**Container Resizing Rules:**
```css
.bilingual-container {
  width: var(--base-width);
  /* Urdu expansion */
  width: calc(var(--base-width) * 1.3) when lang="ur";

  /* RTL layout */
  direction: ltr;
  direction: rtl when lang="ur";

  /* Text alignment */
  text-align: left;
  text-align: right when lang="ur";
}
```

**Icon Mirroring Rules:**
```css
.directional-icon {
  transform: scaleX(1); /* LTR */
  transform: scaleX(-1) when lang="ur"; /* RTL */
}

.non-directional-icon {
  /* Numbers, logos, QR codes - never mirror */
  transform: none !important;
}
```

#### Accessibility Considerations
**Screen Reader Support:**
- **Language Declaration:** Proper lang attributes for each text segment
- **Voice Selection:** Language-appropriate TTS voices
- **Reading Order:** Logical sequence for both LTR and RTL layouts
- **Pronunciation:** Correct pronunciation of mixed-language content

**Keyboard Navigation:**
- **Tab Order:** Reverse for RTL layouts
- **Shortcuts:** Language-appropriate keyboard shortcuts
- **Input Methods:** Support for Urdu input methods

#### Performance Optimization
**Font Loading Strategy:**
```css
@font-face {
  font-family: 'Noto Sans';
  src: url('noto-sans.woff2') format('woff2');
  font-display: swap;
  unicode-range: U+0000-00FF, U+0131, U+0152-0153;
}

@font-face {
  font-family: 'Noto Nastaliq Urdu';
  src: url('noto-nastaliq-urdu.woff2') format('woff2');
  font-display: swap;
  unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;
}
```

**Text Caching:**
- **Translation Cache:** Store translations locally for offline use
- **Font Cache:** Cache fonts with service worker
- **Layout Cache:** Cache calculated layouts for performance

---

*This comprehensive bilingual Figma design specification provides the complete foundation for implementing the Roti Meharbaan mobile application with full English and Urdu language support, cultural sensitivity, accessibility excellence, and professional design standards appropriate for the Pakistani market and Islamic context.*